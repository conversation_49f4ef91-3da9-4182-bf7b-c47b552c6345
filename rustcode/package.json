{"name": "rustcode", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^1.5.0", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "monaco-editor": "^0.44.0"}, "devDependencies": {"@tauri-apps/cli": "^1.5.0", "vite": "^5.0.0"}}
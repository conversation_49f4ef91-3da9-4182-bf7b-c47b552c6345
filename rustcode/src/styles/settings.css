/* Settings Panel Styles */
#settings-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
    align-items: center;
    justify-content: center;
}

.settings-modal {
    background-color: #252526;
    border: 1px solid #3e3e42;
    border-radius: 6px;
    width: 600px;
    max-width: 90vw;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.settings-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    padding: 12px 16px;
}

.settings-header h2 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #cccccc;
}

.settings-close-btn {
    background: none;
    border: none;
    color: #cccccc;
    cursor: pointer;
    font-size: 18px;
    padding: 4px;
    border-radius: 2px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.settings-close-btn:hover {
    background-color: #e74c3c;
    color: white;
}

.settings-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.settings-section {
    margin-bottom: 24px;
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section h3 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #cccccc;
    border-bottom: 1px solid #3e3e42;
    padding-bottom: 6px;
}

.settings-group {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.settings-group label {
    font-size: 13px;
    color: #cccccc;
    flex: 1;
    margin-right: 12px;
}

.settings-checkbox {
    display: flex !important;
    align-items: center;
    cursor: pointer;
    flex: 1 !important;
    margin-right: 0 !important;
}

.settings-checkbox input[type="checkbox"] {
    margin-right: 8px;
    margin-left: 0;
}

.settings-input {
    background-color: #3c3c3c;
    border: 1px solid #3e3e42;
    color: #cccccc;
    font-size: 13px;
    padding: 6px 8px;
    border-radius: 3px;
    outline: none;
    font-family: inherit;
    width: 200px;
    flex-shrink: 0;
}

.settings-input:focus {
    border-color: #007acc;
    box-shadow: 0 0 0 1px #007acc;
}

.settings-select {
    background-color: #3c3c3c;
    border: 1px solid #3e3e42;
    color: #cccccc;
    font-size: 13px;
    padding: 6px 8px;
    border-radius: 3px;
    outline: none;
    font-family: inherit;
    width: 200px;
    flex-shrink: 0;
    cursor: pointer;
}

.settings-select:focus {
    border-color: #007acc;
    box-shadow: 0 0 0 1px #007acc;
}

.settings-select option {
    background-color: #3c3c3c;
    color: #cccccc;
}

.settings-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #3e3e42;
}

.settings-button {
    padding: 8px 16px;
    border: none;
    border-radius: 3px;
    font-size: 13px;
    cursor: pointer;
    font-family: inherit;
}

.settings-button-primary {
    background-color: #007acc;
    color: white;
}

.settings-button-primary:hover {
    background-color: #005a9e;
}

.settings-button-secondary {
    background-color: #3e3e42;
    color: #cccccc;
}

.settings-button-secondary:hover {
    background-color: #4e4e52;
}

/* Theme Styles */

/* Dark Theme (default) */
body.theme-dark {
    --bg-primary: #1e1e1e;
    --bg-secondary: #252526;
    --bg-tertiary: #2d2d30;
    --bg-quaternary: #3c3c3c;
    --border-primary: #3e3e42;
    --text-primary: #cccccc;
    --text-secondary: #888;
    --text-accent: #ffffff;
    --accent-primary: #007acc;
    --accent-secondary: #005a9e;
    --accent-success: #89d185;
    --accent-warning: #f9c74f;
    --accent-error: #f48771;
}

/* Light Theme */
body.theme-light {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f8f8;
    --bg-tertiary: #f0f0f0;
    --bg-quaternary: #e8e8e8;
    --border-primary: #d0d0d0;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-accent: #000000;
    --accent-primary: #0066cc;
    --accent-secondary: #004499;
    --accent-success: #28a745;
    --accent-warning: #ffc107;
    --accent-error: #dc3545;
}

body.theme-light #app {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

body.theme-light .panel {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
}

body.theme-light #title-bar,
body.theme-light #menu-bar,
body.theme-light .sidebar-header,
body.theme-light .terminal-header,
body.theme-light .search-header,
body.theme-light .search-filters,
body.theme-light .search-status {
    background-color: var(--bg-tertiary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

body.theme-light .title-bar-button,
body.theme-light .menu-item,
body.theme-light .sidebar-actions button,
body.theme-light .terminal-actions button {
    color: var(--text-primary);
}

body.theme-light .title-bar-button:hover,
body.theme-light .menu-item:hover,
body.theme-light .sidebar-actions button:hover,
body.theme-light .terminal-actions button:hover {
    background-color: var(--bg-quaternary);
}

/* High Contrast Theme */
body.theme-high-contrast {
    --bg-primary: #000000;
    --bg-secondary: #000000;
    --bg-tertiary: #1a1a1a;
    --bg-quaternary: #333333;
    --border-primary: #ffffff;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-accent: #ffffff;
    --accent-primary: #00ff00;
    --accent-secondary: #00cc00;
    --accent-success: #00ff00;
    --accent-warning: #ffff00;
    --accent-error: #ff0000;
}

body.theme-high-contrast #app {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: 2px solid var(--border-primary);
}

body.theme-high-contrast .panel {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
}

body.theme-high-contrast #title-bar,
body.theme-high-contrast #menu-bar,
body.theme-high-contrast .sidebar-header,
body.theme-high-contrast .terminal-header,
body.theme-high-contrast .search-header {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

body.theme-high-contrast button {
    border: 1px solid var(--border-primary) !important;
    background-color: var(--bg-quaternary) !important;
    color: var(--text-primary) !important;
}

body.theme-high-contrast button:hover {
    background-color: var(--accent-primary) !important;
    color: var(--bg-primary) !important;
}

/* Apply theme variables to existing elements */
body {
    background-color: var(--bg-primary, #1e1e1e);
    color: var(--text-primary, #cccccc);
}

.panel {
    background-color: var(--bg-secondary, #252526);
    border-color: var(--border-primary, #3e3e42);
}

#title-bar,
#menu-bar,
.sidebar-header,
.terminal-header,
.search-header,
.search-filters,
.search-status {
    background-color: var(--bg-tertiary, #2d2d30);
    border-color: var(--border-primary, #3e3e42);
    color: var(--text-primary, #cccccc);
}

.welcome-button,
.search-primary-btn,
.settings-button-primary {
    background-color: var(--accent-primary, #007acc);
}

.welcome-button:hover,
.search-primary-btn:hover,
.settings-button-primary:hover {
    background-color: var(--accent-secondary, #005a9e);
}

/* Scrollbar theming */
::-webkit-scrollbar-track {
    background: var(--bg-primary, #1e1e1e);
}

::-webkit-scrollbar-thumb {
    background: var(--border-primary, #3e3e42);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary, #4e4e52);
}

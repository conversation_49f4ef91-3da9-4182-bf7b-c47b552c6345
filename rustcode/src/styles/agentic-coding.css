/* Modern Agentic Coding Panel Styles */

:root {
    --agentic-primary: #007acc;
    --agentic-primary-hover: #005a9e;
    --agentic-secondary: #00d4aa;
    --agentic-bg-primary: #1e1e1e;
    --agentic-bg-secondary: #252526;
    --agentic-bg-tertiary: #2d2d30;
    --agentic-bg-quaternary: #37373d;
    --agentic-border: #3e3e42;
    --agentic-border-light: #4a4a4f;
    --agentic-text-primary: #cccccc;
    --agentic-text-secondary: #9d9d9d;
    --agentic-text-muted: #6f6f6f;
    --agentic-success: #89d185;
    --agentic-warning: #f9c74f;
    --agentic-error: #f48771;
    --agentic-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --agentic-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.15);
    --agentic-radius: 8px;
    --agentic-radius-small: 4px;
    --agentic-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.agentic-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--agentic-bg-primary);
    color: var(--agentic-text-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    position: relative;
    overflow: hidden;
    border-radius: var(--agentic-radius);
    box-shadow: var(--agentic-shadow);
    backdrop-filter: blur(20px);
}

/* Floating panel animation */
.agentic-panel:not(.hidden) {
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

/* Gradient accent bar */
.agentic-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--agentic-primary), var(--agentic-secondary));
    z-index: 10;
    border-radius: var(--agentic-radius) var(--agentic-radius) 0 0;
}

/* Header Section */
.agentic-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    background: var(--agentic-bg-secondary);
    border-bottom: 1px solid var(--agentic-border);
    position: relative;
    z-index: 5;
}

.agentic-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.agentic-main-icon {
    color: var(--agentic-primary);
    filter: drop-shadow(0 2px 4px rgba(0, 122, 204, 0.3));
}

.title-content h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--agentic-text-primary);
    line-height: 1.2;
}

.title-content .subtitle {
    font-size: 12px;
    color: var(--agentic-text-secondary);
    font-weight: 400;
}

.agentic-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: var(--agentic-bg-tertiary);
    border: 1px solid var(--agentic-border);
    border-radius: var(--agentic-radius-small);
    color: var(--agentic-text-secondary);
    cursor: pointer;
    transition: var(--agentic-transition);
    position: relative;
    overflow: hidden;
}

.control-btn:hover {
    background: var(--agentic-bg-quaternary);
    border-color: var(--agentic-border-light);
    color: var(--agentic-text-primary);
    transform: translateY(-1px);
    box-shadow: var(--agentic-shadow-light);
}

.control-btn.active {
    background: var(--agentic-primary);
    border-color: var(--agentic-primary);
    color: white;
}

.control-btn.active:hover {
    background: var(--agentic-primary-hover);
    border-color: var(--agentic-primary-hover);
}

.control-btn.close-btn:hover {
    background: var(--agentic-error);
    border-color: var(--agentic-error);
    color: white;
}

/* Agentic Mode Section */
.agentic-mode-section {
    padding: 16px 24px;
    border-bottom: 1px solid var(--agentic-border);
    background: var(--agentic-bg-secondary);
}

.toggle-switch {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    user-select: none;
}

.toggle-switch input {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background: var(--agentic-bg-quaternary);
    border: 1px solid var(--agentic-border);
    border-radius: 12px;
    transition: var(--agentic-transition);
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 18px;
    height: 18px;
    background: var(--agentic-text-secondary);
    border-radius: 50%;
    transition: var(--agentic-transition);
}

.toggle-switch input:checked + .toggle-slider {
    background: var(--agentic-primary);
    border-color: var(--agentic-primary);
}

.toggle-switch input:checked + .toggle-slider::before {
    transform: translateX(20px);
    background: white;
}

.toggle-label {
    font-size: 13px;
    font-weight: 500;
    color: var(--agentic-text-primary);
}

/* Features section removed for streamlined chat interface */

/* Chat Section - Full Height */
.agentic-chat-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow flex shrinking */
    overflow: hidden;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px 24px;
    min-height: 0; /* Allow flex shrinking */
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    margin-bottom: 16px;
    min-height: 200px; /* Minimum height for usability */
}

.welcome-message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: var(--agentic-bg-tertiary);
    border: 1px solid var(--agentic-border);
    border-radius: var(--agentic-radius);
    margin-bottom: 16px;
}

.welcome-icon {
    color: var(--agentic-secondary);
    flex-shrink: 0;
}

.welcome-content h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--agentic-text-primary);
}

.welcome-content p {
    margin: 0;
    font-size: 13px;
    color: var(--agentic-text-secondary);
    line-height: 1.4;
}

.welcome-content .config-notice {
    margin-top: 8px;
    padding: 8px 12px;
    background: var(--agentic-warning);
    color: var(--agentic-bg-primary);
    border-radius: var(--agentic-radius-small);
    font-size: 12px;
    font-weight: 500;
}

.chat-message {
    margin-bottom: 16px;
    padding: 16px;
    border-radius: var(--agentic-radius);
    background: var(--agentic-bg-tertiary);
    border: 1px solid var(--agentic-border);
    position: relative;
}

.chat-message.user {
    background: var(--agentic-primary);
    border-color: var(--agentic-primary);
    color: white;
    margin-left: 15%;
}

.chat-message.assistant {
    background: var(--agentic-bg-quaternary);
    border-color: var(--agentic-border-light);
    margin-right: 15%;
}

.chat-message.system {
    background: var(--agentic-warning);
    border-color: var(--agentic-warning);
    color: var(--agentic-bg-primary);
    text-align: center;
    margin: 12px 0;
    font-style: italic;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 12px;
    opacity: 0.8;
}

.message-content {
    font-size: 13px;
    line-height: 1.5;
}

.message-content pre {
    background: var(--agentic-bg-primary);
    border: 1px solid var(--agentic-border);
    padding: 12px;
    border-radius: var(--agentic-radius-small);
    overflow-x: auto;
    margin: 12px 0;
    font-size: 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.message-content code {
    background: var(--agentic-bg-primary);
    border: 1px solid var(--agentic-border);
    padding: 2px 6px;
    border-radius: var(--agentic-radius-small);
    font-size: 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.chat-input-container {
    padding: 16px 0 0;
    border-top: 1px solid var(--agentic-border);
}

.input-wrapper {
    display: flex;
    align-items: center;
    background: var(--agentic-bg-tertiary);
    border: 1px solid var(--agentic-border);
    border-radius: var(--agentic-radius);
    overflow: hidden;
    transition: var(--agentic-transition);
}

.input-wrapper:focus-within {
    border-color: var(--agentic-primary);
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

#agentic-input {
    flex: 1;
    background: transparent;
    border: none;
    color: var(--agentic-text-primary);
    padding: 12px 16px;
    font-size: 14px;
    outline: none;
    font-family: inherit;
}

#agentic-input::placeholder {
    color: var(--agentic-text-muted);
}

#agentic-input:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.send-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: var(--agentic-primary);
    border: none;
    color: white;
    cursor: pointer;
    transition: var(--agentic-transition);
}

.send-btn:hover:not(:disabled) {
    background: var(--agentic-primary-hover);
}

.send-btn:disabled {
    background: var(--agentic-bg-quaternary);
    color: var(--agentic-text-muted);
    cursor: not-allowed;
}

/* Tasks section removed for streamlined chat interface */

/* Settings Modal */
.agentic-settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease-out;
}

.agentic-settings-modal.hidden {
    display: none;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--agentic-bg-primary);
    border: 1px solid var(--agentic-border);
    border-radius: var(--agentic-radius);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--agentic-shadow);
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--agentic-border);
    background: var(--agentic-bg-secondary);
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--agentic-text-primary);
}

.modal-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--agentic-bg-tertiary);
    border: 1px solid var(--agentic-border);
    border-radius: var(--agentic-radius-small);
    color: var(--agentic-text-secondary);
    cursor: pointer;
    transition: var(--agentic-transition);
}

.modal-close:hover {
    background: var(--agentic-error);
    border-color: var(--agentic-error);
    color: white;
}

.modal-body {
    padding: 24px;
}

.setting-group {
    margin-bottom: 24px;
}

.setting-group:last-child {
    margin-bottom: 0;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: var(--agentic-text-primary);
}

.setting-group input,
.setting-group select {
    width: 100%;
    background: var(--agentic-bg-tertiary);
    border: 1px solid var(--agentic-border);
    color: var(--agentic-text-primary);
    padding: 12px 16px;
    border-radius: var(--agentic-radius-small);
    font-size: 14px;
    outline: none;
    transition: var(--agentic-transition);
    font-family: inherit;
}

.setting-group input:focus,
.setting-group select:focus {
    border-color: var(--agentic-primary);
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.setting-group input[type="range"] {
    padding: 0;
    height: 6px;
    background: var(--agentic-bg-quaternary);
    border-radius: 3px;
    appearance: none;
}

.setting-group input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    background: var(--agentic-primary);
    border-radius: 50%;
    cursor: pointer;
}

.setting-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

.setting-group small {
    display: block;
    margin-top: 6px;
    color: var(--agentic-text-secondary);
    font-size: 12px;
    line-height: 1.4;
}

.setting-group small a {
    color: var(--agentic-primary);
    text-decoration: none;
}

.setting-group small a:hover {
    text-decoration: underline;
}

.range-value {
    margin-left: 12px;
    font-size: 13px;
    font-weight: 500;
    color: var(--agentic-primary);
    background: var(--agentic-bg-tertiary);
    padding: 2px 8px;
    border-radius: var(--agentic-radius-small);
    border: 1px solid var(--agentic-border);
}

.modal-footer {
    display: flex;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid var(--agentic-border);
    background: var(--agentic-bg-secondary);
    justify-content: flex-end;
}

.primary-btn,
.secondary-btn {
    padding: 12px 20px;
    border-radius: var(--agentic-radius-small);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: var(--agentic-transition);
    font-family: inherit;
}

.primary-btn {
    background: var(--agentic-primary);
    color: white;
}

.primary-btn:hover {
    background: var(--agentic-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--agentic-shadow-light);
}

.secondary-btn {
    background: var(--agentic-bg-tertiary);
    color: var(--agentic-text-primary);
    border: 1px solid var(--agentic-border);
}

.secondary-btn:hover {
    background: var(--agentic-bg-quaternary);
    border-color: var(--agentic-border-light);
    transform: translateY(-1px);
}

/* Scrollbar Styling */
.agentic-panel ::-webkit-scrollbar {
    width: 6px;
}

.agentic-panel ::-webkit-scrollbar-track {
    background: var(--agentic-bg-secondary);
}

.agentic-panel ::-webkit-scrollbar-thumb {
    background: var(--agentic-border);
    border-radius: 3px;
}

.agentic-panel ::-webkit-scrollbar-thumb:hover {
    background: var(--agentic-border-light);
}

/* Loading Animation */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.loading {
    animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 480px) {
    .agentic-panel {
        width: 100% !important;
        height: 100% !important;
        top: 0 !important;
        right: 0 !important;
        border-radius: 0;
    }

    .chat-container {
        padding: 16px;
    }

    .agentic-header {
        padding: 16px;
    }

    .agentic-mode-section {
        padding: 12px 16px;
    }
}

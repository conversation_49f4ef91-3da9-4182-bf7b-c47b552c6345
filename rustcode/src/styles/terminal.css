/* Terminal Panel Styles */
#terminal-panel {
    background-color: #1e1e1e;
    border-top: 1px solid #3e3e42;
    display: flex;
    flex-direction: column;
    height: 300px;
    min-height: 100px;
    max-height: 60vh;
}

.terminal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    padding: 0 10px;
    height: 35px;
    min-height: 35px;
}

.terminal-tabs {
    display: flex;
    gap: 2px;
    flex: 1;
    overflow-x: auto;
}

.terminal-tab {
    display: flex;
    align-items: center;
    background-color: #3c3c3c;
    border: 1px solid #3e3e42;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
    color: #cccccc;
    min-width: 120px;
    max-width: 200px;
    position: relative;
}

.terminal-tab:hover {
    background-color: #404040;
}

.terminal-tab.active {
    background-color: #1e1e1e;
    border-color: #007acc;
    color: #ffffff;
}

.terminal-tab-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 8px;
}

.terminal-tab-close {
    background: none;
    border: none;
    color: #cccccc;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
}

.terminal-tab-close:hover {
    background-color: #e74c3c;
    color: white;
}

.terminal-actions {
    display: flex;
    gap: 4px;
    margin-left: 10px;
}

.terminal-actions button {
    background: none;
    border: none;
    color: #cccccc;
    cursor: pointer;
    padding: 4px 6px;
    border-radius: 2px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.terminal-actions button:hover {
    background-color: #3e3e42;
}

.terminal-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.terminal-instance {
    width: 100%;
    height: 100%;
    padding: 8px;
}

/* XTerm.js overrides */
.terminal-instance .xterm {
    height: 100% !important;
}

.terminal-instance .xterm-viewport {
    background-color: transparent !important;
}

.terminal-instance .xterm-screen {
    background-color: transparent !important;
}

/* Terminal resize handle */
.terminal-resize-handle {
    height: 4px;
    background-color: #3e3e42;
    cursor: row-resize;
    position: relative;
}

.terminal-resize-handle:hover {
    background-color: #007acc;
}

/* Terminal panel states */
.terminal-panel-hidden {
    display: none !important;
}

.terminal-panel-minimized {
    height: 35px !important;
}

.terminal-panel-minimized .terminal-content {
    display: none;
}

/* Terminal scrollbar */
.terminal-instance .xterm .xterm-viewport::-webkit-scrollbar {
    width: 10px;
}

.terminal-instance .xterm .xterm-viewport::-webkit-scrollbar-track {
    background: #1e1e1e;
}

.terminal-instance .xterm .xterm-viewport::-webkit-scrollbar-thumb {
    background: #3e3e42;
    border-radius: 5px;
}

.terminal-instance .xterm .xterm-viewport::-webkit-scrollbar-thumb:hover {
    background: #4e4e52;
}

/* Terminal selection */
.terminal-instance .xterm .xterm-selection div {
    background-color: #264f78 !important;
}

/* Terminal cursor */
.terminal-instance .xterm .xterm-cursor-layer .xterm-cursor {
    background-color: #ffffff !important;
}

/* Terminal links */
.terminal-instance .xterm .xterm-link {
    color: #4fc3f7 !important;
    text-decoration: underline;
}

.terminal-instance .xterm .xterm-link:hover {
    color: #29b6f6 !important;
}

/* Terminal search */
.terminal-search {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #2d2d30;
    border: 1px solid #3e3e42;
    border-radius: 4px;
    padding: 4px 8px;
    display: none;
    z-index: 10;
}

.terminal-search input {
    background: none;
    border: none;
    color: #cccccc;
    font-size: 12px;
    outline: none;
    width: 150px;
}

.terminal-search button {
    background: none;
    border: none;
    color: #cccccc;
    cursor: pointer;
    margin-left: 4px;
    padding: 2px;
}

.terminal-search button:hover {
    color: #ffffff;
}

/* Terminal context menu */
.terminal-context-menu {
    position: absolute;
    background-color: #2d2d30;
    border: 1px solid #3e3e42;
    border-radius: 4px;
    padding: 4px 0;
    min-width: 120px;
    z-index: 1000;
    display: none;
}

.terminal-context-menu-item {
    padding: 6px 12px;
    cursor: pointer;
    font-size: 12px;
    color: #cccccc;
}

.terminal-context-menu-item:hover {
    background-color: #3e3e42;
}

.terminal-context-menu-separator {
    height: 1px;
    background-color: #3e3e42;
    margin: 4px 0;
}

/* Terminal status indicators */
.terminal-status {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: rgba(45, 45, 48, 0.9);
    border: 1px solid #3e3e42;
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 10px;
    color: #888;
    pointer-events: none;
}

/* Terminal animations */
.terminal-tab {
    transition: background-color 0.1s ease;
}

.terminal-actions button {
    transition: background-color 0.1s ease;
}

.terminal-resize-handle {
    transition: background-color 0.1s ease;
}

/* Responsive design */
@media (max-width: 768px) {
    .terminal-tab {
        min-width: 80px;
        max-width: 120px;
    }
    
    .terminal-tab-name {
        font-size: 11px;
    }
    
    #terminal-panel {
        height: 250px;
    }
}

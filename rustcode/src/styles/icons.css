/* Icon System Styles */

.icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    user-select: none;
    pointer-events: none;
    position: relative;
    overflow: hidden;
}

.icon svg {
    width: 100%;
    height: 100%;
    color: inherit;
    fill: currentColor;
    display: block;
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* Ensure SVG paths inherit color properly */
.icon svg path {
    color: inherit;
}

.icon svg circle {
    color: inherit;
}

.icon svg rect {
    color: inherit;
}

/* Icon Sizes */
.icon-xs {
    width: 12px;
    height: 12px;
}

.icon-sm {
    width: 14px;
    height: 14px;
}

.icon-md {
    width: 16px;
    height: 16px;
}

.icon-lg {
    width: 20px;
    height: 20px;
}

.icon-xl {
    width: 24px;
    height: 24px;
}

/* Icon Colors */
.icon-primary {
    color: var(--accent-primary, #007acc);
}

.icon-secondary {
    color: var(--text-secondary, #888);
}

.icon-success {
    color: var(--accent-success, #89d185);
}

.icon-warning {
    color: var(--accent-warning, #f9c74f);
}

.icon-error {
    color: var(--accent-error, #f48771);
}

.icon-muted {
    color: var(--text-secondary, #888);
    opacity: 0.6;
}

/* Interactive Icons */
.icon-button {
    cursor: pointer;
    pointer-events: auto;
    border-radius: 2px;
    padding: 2px;
    transition: background-color 0.1s ease, color 0.1s ease;
}

.icon-button:hover {
    background-color: var(--bg-quaternary, #3e3e42);
}

.icon-button:active {
    background-color: var(--bg-tertiary, #2d2d30);
}

.icon-button.active {
    background-color: var(--accent-primary, #007acc);
    color: white;
}

/* File Type Icons */
.file-icon {
    margin-right: 6px;
    flex-shrink: 0;
}

.file-icon.folder {
    color: var(--accent-primary, #007acc);
}

.file-icon.javascript {
    color: #F7DF1E;
}

.file-icon.rust {
    color: #CE422B;
}

.file-icon.python {
    color: #3776AB;
}

.file-icon.html {
    color: #E34F26;
}

.file-icon.css {
    color: #1572B6;
}

.file-icon.json {
    color: #FFD700;
}

.file-icon.markdown {
    color: #083FA1;
}

/* Status Icons */
.status-icon {
    margin-right: 4px;
}

.status-icon.success {
    color: var(--accent-success, #89d185);
}

.status-icon.warning {
    color: var(--accent-warning, #f9c74f);
}

.status-icon.error {
    color: var(--accent-error, #f48771);
}

.status-icon.info {
    color: var(--accent-primary, #007acc);
}

/* Loading Animation */
.icon.loading {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Menu Icons */
.menu-icon {
    margin-right: 8px;
    color: var(--text-secondary, #888);
}

/* Tab Icons */
.tab-icon {
    margin-right: 6px;
    flex-shrink: 0;
}

.tab-close-icon {
    margin-left: 6px;
    opacity: 0;
    transition: opacity 0.1s ease;
}

.tab:hover .tab-close-icon {
    opacity: 1;
}

.tab-close-icon:hover {
    background-color: var(--accent-error, #f48771);
    color: white;
    border-radius: 2px;
}

/* Sidebar Icons */
.sidebar-icon {
    color: var(--text-secondary, #888);
    transition: color 0.1s ease;
}

.sidebar-icon:hover {
    color: var(--text-primary, #cccccc);
}

/* Terminal Icons */
.terminal-icon {
    color: var(--text-secondary, #888);
}

/* Search Icons */
.search-icon {
    color: var(--text-secondary, #888);
}

.search-icon.active {
    color: var(--accent-primary, #007acc);
}

/* Settings Icons */
.settings-icon {
    margin-right: 8px;
    color: var(--text-secondary, #888);
}

/* Context Menu Icons */
.context-menu-icon {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    color: var(--text-secondary, #888);
}

/* Breadcrumb Icons */
.breadcrumb-icon {
    margin: 0 4px;
    color: var(--text-secondary, #888);
}

/* Notification Icons */
.notification-icon {
    margin-right: 8px;
    flex-shrink: 0;
}

/* Tree View Icons */
.tree-icon {
    margin-right: 4px;
    color: var(--text-secondary, #888);
    transition: transform 0.1s ease;
}

.tree-icon.expanded {
    transform: rotate(90deg);
}

/* Button Icons */
.button-icon {
    margin-right: 4px;
}

.button-icon-only {
    margin: 0;
}

/* Responsive Icon Adjustments */
@media (max-width: 768px) {
    .icon-lg {
        width: 18px;
        height: 18px;
    }

    .icon-xl {
        width: 20px;
        height: 20px;
    }

    .file-icon {
        margin-right: 4px;
    }

    .menu-icon {
        margin-right: 6px;
    }
}

/* High Contrast Theme Adjustments */
body.theme-high-contrast .icon {
    filter: contrast(1.5);
}

body.theme-high-contrast .icon-button:hover {
    background-color: var(--accent-primary, #00ff00);
    color: var(--bg-primary, #000000);
}

/* Light Theme Adjustments */
body.theme-light .file-icon.javascript {
    color: #B8860B;
}

body.theme-light .file-icon.rust {
    color: #A0522D;
}

body.theme-light .file-icon.python {
    color: #2E5984;
}

body.theme-light .file-icon.html {
    color: #C4371F;
}

body.theme-light .file-icon.css {
    color: #1261A0;
}

body.theme-light .file-icon.json {
    color: #B8860B;
}

body.theme-light .file-icon.markdown {
    color: #062F7A;
}

/* Accessibility */
.icon[aria-label] {
    cursor: help;
}

.icon-button[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Print Styles */
@media print {
    .icon {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}

/* Debug styles for empty icons - only in development */
.icon:empty {
    background-color: rgba(255, 0, 0, 0.1);
    border: 1px dashed rgba(255, 0, 0, 0.3);
    position: relative;
}

.icon:empty::after {
    content: "?";
    color: rgba(255, 0, 0, 0.7);
    font-size: 10px;
    font-weight: bold;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Ensure proper rendering for all icon containers */
.icon {
    min-width: 12px;
    min-height: 12px;
}

/* Fix for icons that might have rendering issues */
.icon svg {
    shape-rendering: geometricPrecision;
    text-rendering: optimizeLegibility;
}

/* Fallback for broken icons */
.icon[data-icon-missing="true"] {
    background-color: rgba(255, 165, 0, 0.1);
    border: 1px solid rgba(255, 165, 0, 0.3);
}

.icon[data-icon-missing="true"]::after {
    content: "⚠";
    color: orange;
    font-size: 12px;
}

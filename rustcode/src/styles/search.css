/* Search Panel Styles */
#search-panel {
    background-color: #252526;
    border-right: 1px solid #3e3e42;
    width: 350px;
    min-width: 300px;
    max-width: 500px;
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.search-header {
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    padding: 10px;
}

.search-input-container {
    position: relative;
    margin-bottom: 8px;
}

.search-input {
    width: 100%;
    background-color: #3c3c3c;
    border: 1px solid #3e3e42;
    color: #cccccc;
    font-size: 13px;
    padding: 6px 8px;
    padding-right: 80px;
    border-radius: 3px;
    outline: none;
    font-family: inherit;
}

.search-input:focus {
    border-color: #007acc;
    box-shadow: 0 0 0 1px #007acc;
}

.search-input-actions {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 2px;
}

.search-toggle-btn {
    background: none;
    border: none;
    color: #cccccc;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 11px;
    font-family: monospace;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-toggle-btn:hover {
    background-color: #3e3e42;
}

.search-toggle-btn.active {
    background-color: #007acc;
    color: white;
}

.search-replace-container {
    position: relative;
    margin-bottom: 8px;
}

.search-replace-actions {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 2px;
}

.search-action-btn {
    background-color: #0e639c;
    border: none;
    color: white;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 11px;
    height: 20px;
}

.search-action-btn:hover {
    background-color: #1177bb;
}

.search-action-btn:disabled {
    background-color: #3e3e42;
    color: #888;
    cursor: not-allowed;
}

.search-controls {
    display: flex;
    gap: 4px;
    align-items: center;
}

.search-primary-btn {
    background-color: #007acc;
    border: none;
    color: white;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 3px;
    font-size: 12px;
    flex: 1;
}

.search-primary-btn:hover {
    background-color: #005a9e;
}

.search-primary-btn:disabled {
    background-color: #3e3e42;
    color: #888;
    cursor: not-allowed;
}

.search-close-btn {
    background: none;
    border: none;
    color: #cccccc;
    cursor: pointer;
    padding: 4px;
    border-radius: 2px;
    font-size: 14px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-close-btn:hover {
    background-color: #e74c3c;
    color: white;
}

.search-filters {
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    padding: 8px 10px;
}

.search-filter-group {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
}

.search-filter-group:last-child {
    margin-bottom: 0;
}

.search-filter-group label {
    font-size: 11px;
    color: #cccccc;
    width: 60px;
    flex-shrink: 0;
}

.search-filter-input {
    flex: 1;
    background-color: #3c3c3c;
    border: 1px solid #3e3e42;
    color: #cccccc;
    font-size: 11px;
    padding: 4px 6px;
    border-radius: 2px;
    outline: none;
    font-family: inherit;
}

.search-filter-input:focus {
    border-color: #007acc;
}

.search-status {
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    padding: 6px 10px;
    font-size: 11px;
    color: #cccccc;
    position: relative;
}

.search-status-error {
    color: #f48771;
}

.search-status-success {
    color: #89d185;
}

.search-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #3e3e42;
}

.search-progress-bar {
    height: 100%;
    background-color: #007acc;
    width: 0%;
    transition: width 0.2s ease;
}

.search-results {
    flex: 1;
    overflow-y: auto;
    background-color: #252526;
}

.search-results-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #888;
    text-align: center;
    padding: 20px;
}

.search-results-empty p {
    margin: 4px 0;
    font-size: 13px;
}

.search-results-hint {
    font-size: 11px !important;
    color: #666 !important;
}

.search-results-list {
    padding: 8px 0;
}

.search-result-file {
    margin-bottom: 12px;
}

.search-result-file-header {
    display: flex;
    align-items: center;
    padding: 4px 10px;
    cursor: pointer;
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
}

.search-result-file-header:hover {
    background-color: #37373d;
}

.search-result-file-icon {
    margin-right: 6px;
    font-size: 12px;
}

.search-result-file-name {
    font-weight: bold;
    color: #cccccc;
    font-size: 12px;
    margin-right: 8px;
}

.search-result-file-path {
    flex: 1;
    color: #888;
    font-size: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.search-result-file-count {
    background-color: #007acc;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

.search-result-matches {
    background-color: #252526;
}

.search-result-match {
    display: flex;
    align-items: flex-start;
    padding: 2px 10px;
    cursor: pointer;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
}

.search-result-match:hover {
    background-color: #2a2d2e;
}

.search-result-line-number {
    color: #858585;
    margin-right: 12px;
    min-width: 30px;
    text-align: right;
    flex-shrink: 0;
    padding-top: 1px;
}

.search-result-line-text {
    color: #cccccc;
    word-break: break-all;
    flex: 1;
}

.search-result-line-text mark {
    background-color: #f9c74f;
    color: #000;
    padding: 0 1px;
    border-radius: 1px;
}

/* Scrollbar for search results */
.search-results::-webkit-scrollbar {
    width: 10px;
}

.search-results::-webkit-scrollbar-track {
    background: #252526;
}

.search-results::-webkit-scrollbar-thumb {
    background: #3e3e42;
    border-radius: 5px;
}

.search-results::-webkit-scrollbar-thumb:hover {
    background: #4e4e52;
}

/* Search panel resize handle */
.search-resize-handle {
    width: 4px;
    background-color: #3e3e42;
    cursor: col-resize;
    position: relative;
}

.search-resize-handle:hover {
    background-color: #007acc;
}

/* Responsive design */
@media (max-width: 768px) {
    #search-panel {
        width: 100%;
        max-width: none;
    }
    
    .search-input-actions {
        position: static;
        transform: none;
        margin-top: 4px;
        justify-content: flex-end;
    }
    
    .search-input {
        padding-right: 8px;
    }
    
    .search-replace-actions {
        position: static;
        transform: none;
        margin-top: 4px;
        justify-content: flex-end;
    }
}

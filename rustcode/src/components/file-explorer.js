// Check if we're running in Tauri or web mode
const isTauri = typeof window !== 'undefined' && window.__TAURI_IPC__;

let invoke;
if (isTauri) {
    const tauriModule = await import('@tauri-apps/api/tauri');
    invoke = tauriModule.invoke;
} else {
    // Use the web-based file system
    invoke = async (command, args) => {
        // Access the global web file system
        if (window.rustCodeApp && window.rustCodeApp.invoke) {
            return window.rustCodeApp.invoke(command, args);
        }

        console.log(`File explorer invoke: ${command}`, args);
        return null;
    };
}

import { createIcon, getFileTypeIcon } from '../icons/icon-library.js';

export class FileExplorer {
    constructor(container, onFileSelect, onFileAction) {
        this.container = container;
        this.onFileSelect = onFileSelect;
        this.onFileAction = onFileAction;
        this.expandedFolders = new Set();
        this.clipboard = null;
        this.clipboardOperation = null; // 'cut' or 'copy'
        this.draggedItem = null;
        this.renamingItem = null;

        this.init();
    }

    init() {
        this.container.innerHTML = '<div class="file-tree-loading">No folder opened</div>';
    }

    async loadDirectory(path) {
        try {
            this.container.innerHTML = '<div class="file-tree-loading">Loading...</div>';

            const tree = await invoke('get_directory_tree', {
                path: path,
                maxDepth: 3
            });

            this.renderTree(tree);
        } catch (error) {
            console.error('Failed to load directory:', error);
            this.container.innerHTML = `<div class="file-tree-empty">Failed to load directory: ${error}</div>`;
        }
    }

    renderTree(node, level = 0) {
        if (level === 0) {
            this.container.innerHTML = '';
            const ul = document.createElement('ul');
            ul.className = 'file-tree';
            this.container.appendChild(ul);
            this.renderNode(node, ul, level);
        } else {
            const ul = document.createElement('ul');
            ul.className = 'file-tree-children';
            return ul;
        }
    }

    renderNode(node, parentElement, level) {
        const li = document.createElement('li');
        li.className = 'file-tree-item';
        li.dataset.path = node.path;
        li.dataset.isDirectory = node.is_directory;

        // Create indentation
        for (let i = 0; i < level; i++) {
            const indent = document.createElement('span');
            indent.className = 'file-tree-indent';
            li.appendChild(indent);
        }

        // Create arrow for directories
        if (node.is_directory) {
            const arrow = document.createElement('span');
            arrow.className = 'file-tree-arrow';

            const isExpanded = this.expandedFolders.has(node.path);
            arrow.innerHTML = createIcon(isExpanded ? 'chevronDown' : 'chevronRight', 12, 'tree-icon');

            if (isExpanded) {
                arrow.classList.add('expanded');
            }

            arrow.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleFolder(node, li, arrow);
            });

            li.appendChild(arrow);
        } else {
            const spacer = document.createElement('span');
            spacer.className = 'file-tree-indent';
            li.appendChild(spacer);
        }

        // Create icon with file type detection
        const icon = document.createElement('span');
        icon.className = `file-tree-icon ${node.is_directory ? 'folder' : 'file'}`;

        if (node.is_directory) {
            const isExpanded = this.expandedFolders.has(node.path);
            icon.innerHTML = createIcon(isExpanded ? 'folderOpen' : 'folder', 16, 'file-icon folder');
            if (isExpanded) {
                icon.classList.add('open');
            }
        } else {
            const fileTypeIcon = getFileTypeIcon(node.name);
            icon.innerHTML = createIcon(fileTypeIcon, 16, `file-icon ${fileTypeIcon}`);
            const ext = this.getFileExtension(node.name);
            icon.dataset.ext = ext;
        }

        li.appendChild(icon);

        // Create label
        const label = document.createElement('span');
        label.className = 'file-tree-label';
        label.textContent = node.name;
        li.appendChild(label);

        // Add click handler
        li.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectItem(li);

            if (!node.is_directory) {
                this.onFileSelect(node.path);
            } else {
                this.toggleFolder(node, li, arrow);
            }
        });

        // Add double-click handler for rename
        li.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            if (e.ctrlKey || e.metaKey) {
                this.startRename(li, node);
            }
        });

        // Add context menu handler
        li.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.selectItem(li);
            this.showContextMenu(e, node);
        });

        // Add drag and drop handlers
        li.draggable = true;
        li.addEventListener('dragstart', (e) => {
            this.handleDragStart(e, node, li);
        });

        li.addEventListener('dragover', (e) => {
            this.handleDragOver(e, node, li);
        });

        li.addEventListener('drop', (e) => {
            this.handleDrop(e, node, li);
        });

        li.addEventListener('dragend', (e) => {
            this.handleDragEnd(e);
        });

        parentElement.appendChild(li);

        // Add children if directory is expanded
        if (node.is_directory && node.children && this.expandedFolders.has(node.path)) {
            const childrenContainer = document.createElement('ul');
            childrenContainer.className = 'file-tree-children';

            node.children.forEach(child => {
                this.renderNode(child, childrenContainer, level + 1);
            });

            parentElement.appendChild(childrenContainer);
        }
    }

    async toggleFolder(node, li, arrow) {
        if (this.expandedFolders.has(node.path)) {
            // Collapse
            this.expandedFolders.delete(node.path);
            arrow.classList.remove('expanded');
            arrow.innerHTML = createIcon('chevronRight', 12, 'tree-icon');

            const icon = li.querySelector('.file-tree-icon');
            icon.innerHTML = createIcon('folder', 16, 'file-icon folder');
            icon.classList.remove('open');

            // Remove children
            const children = li.parentElement.querySelector('.file-tree-children');
            if (children) {
                children.remove();
            }
        } else {
            // Expand
            this.expandedFolders.add(node.path);
            arrow.classList.add('expanded');
            arrow.innerHTML = createIcon('chevronDown', 12, 'tree-icon');

            const icon = li.querySelector('.file-tree-icon');
            icon.innerHTML = createIcon('folderOpen', 16, 'file-icon folder');
            icon.classList.add('open');

            // Load and add children if not already loaded
            try {
                const tree = await invoke('get_directory_tree', {
                    path: node.path,
                    maxDepth: 1
                });

                if (tree.children && tree.children.length > 0) {
                    const childrenContainer = document.createElement('ul');
                    childrenContainer.className = 'file-tree-children';

                    tree.children.forEach(child => {
                        this.renderNode(child, childrenContainer, this.getNodeLevel(li) + 1);
                    });

                    li.parentElement.appendChild(childrenContainer);
                }
            } catch (error) {
                console.error('Failed to load folder contents:', error);
            }
        }
    }

    selectItem(item) {
        // Remove previous selection
        const previousSelected = this.container.querySelector('.file-tree-item.selected');
        if (previousSelected) {
            previousSelected.classList.remove('selected');
        }

        // Add selection to current item
        item.classList.add('selected');
    }

    showContextMenu(event, node) {
        const contextMenu = document.getElementById('context-menu');

        // Clear existing menu items
        contextMenu.innerHTML = '';

        // Create menu items based on node type and context
        const menuItems = this.getContextMenuItems(node);

        menuItems.forEach(item => {
            if (item.separator) {
                const separator = document.createElement('div');
                separator.className = 'context-menu-separator';
                contextMenu.appendChild(separator);
            } else {
                const menuItem = document.createElement('div');
                menuItem.className = 'context-menu-item';

                // Add icon if provided
                if (item.icon) {
                    const iconSpan = document.createElement('span');
                    iconSpan.innerHTML = createIcon(item.icon, 14, 'context-menu-icon');
                    menuItem.appendChild(iconSpan);
                }

                const labelSpan = document.createElement('span');
                labelSpan.textContent = item.label;
                menuItem.appendChild(labelSpan);

                menuItem.dataset.action = item.action;

                if (item.disabled) {
                    menuItem.classList.add('disabled');
                } else {
                    menuItem.onclick = () => {
                        this.handleContextMenuAction(item.action, node);
                        contextMenu.classList.add('hidden');
                    };
                }

                contextMenu.appendChild(menuItem);
            }
        });

        // Position the context menu
        contextMenu.style.left = event.pageX + 'px';
        contextMenu.style.top = event.pageY + 'px';
        contextMenu.classList.remove('hidden');
    }

    getContextMenuItems(node) {
        const items = [];

        if (!node.is_directory) {
            items.push({ label: 'Open', action: 'open', icon: 'open' });
            items.push({ separator: true });
        }

        items.push({ label: 'Rename', action: 'rename', icon: 'edit' });
        items.push({ label: 'Delete', action: 'delete', icon: 'trash' });
        items.push({ separator: true });
        items.push({ label: 'Cut', action: 'cut', icon: 'cut' });
        items.push({ label: 'Copy', action: 'copy', icon: 'copy' });

        if (this.clipboard && node.is_directory) {
            items.push({ label: 'Paste', action: 'paste', icon: 'paste' });
        }

        items.push({ separator: true });
        items.push({ label: 'New File', action: 'new-file', icon: 'file' });
        items.push({ label: 'New Folder', action: 'new-folder', icon: 'folder' });

        if (node.is_directory) {
            items.push({ separator: true });
            items.push({ label: 'Reveal in Explorer', action: 'reveal', icon: 'search' });
        }

        return items;
    }

    async handleContextMenuAction(action, node) {
        switch (action) {
            case 'open':
                if (!node.is_directory) {
                    this.onFileSelect(node.path);
                }
                break;
            case 'rename':
                const li = this.container.querySelector(`[data-path="${node.path}"]`);
                this.startRename(li, node);
                break;
            case 'delete':
                await this.deleteItem(node);
                break;
            case 'cut':
                this.cutItem(node);
                break;
            case 'copy':
                this.copyItem(node);
                break;
            case 'paste':
                await this.pasteItem(node);
                break;
            case 'new-file':
                await this.createNewFile(node.is_directory ? node.path : this.getParentPath(node.path));
                break;
            case 'new-folder':
                await this.createNewFolder(node.is_directory ? node.path : this.getParentPath(node.path));
                break;
            case 'reveal':
                console.log('Reveal in explorer:', node.path);
                break;
            default:
                this.onFileAction(action, node.path);
        }
    }

    getNodeLevel(element) {
        let level = 0;
        let current = element;

        while (current && current !== this.container) {
            if (current.classList.contains('file-tree-children')) {
                level++;
            }
            current = current.parentElement;
        }

        return level;
    }

    getFileExtension(filename) {
        const parts = filename.split('.');
        return parts.length > 1 ? parts.pop().toLowerCase() : '';
    }

    getFileIcon(extension) {
        const iconMap = {
            'rs': '🦀',
            'js': '📜',
            'mjs': '📜',
            'ts': '📘',
            'py': '🐍',
            'html': '🌐',
            'htm': '🌐',
            'css': '🎨',
            'json': '📋',
            'xml': '📄',
            'md': '📝',
            'txt': '📄',
            'yaml': '⚙️',
            'yml': '⚙️',
            'toml': '⚙️',
            'sh': '💻',
            'bash': '💻',
            'c': '📄',
            'cpp': '📄',
            'h': '📄',
            'hpp': '📄',
            'java': '☕',
            'go': '🐹',
            'php': '🐘',
            'rb': '💎',
            'swift': '🦉',
            'kt': '📱',
            'scala': '📄',
            'sql': '🗃️',
            'dockerfile': '🐳',
            'gitignore': '📄',
            'license': '📄',
            'readme': '📖'
        };

        return iconMap[extension] || '📄';
    }

    // Drag and Drop Methods
    handleDragStart(e, node, li) {
        this.draggedItem = { node, element: li };
        li.classList.add('dragging');
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/plain', node.path);
    }

    handleDragOver(e, node, li) {
        if (!this.draggedItem || this.draggedItem.node.path === node.path) {
            return;
        }

        if (node.is_directory) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
            li.classList.add('drag-over');
        }
    }

    handleDrop(e, node, li) {
        e.preventDefault();
        li.classList.remove('drag-over');

        if (!this.draggedItem || this.draggedItem.node.path === node.path) {
            return;
        }

        if (node.is_directory) {
            this.moveItem(this.draggedItem.node, node);
        }
    }

    handleDragEnd(e) {
        if (this.draggedItem) {
            this.draggedItem.element.classList.remove('dragging');
            this.draggedItem = null;
        }

        // Remove all drag-over classes
        const dragOverItems = this.container.querySelectorAll('.drag-over');
        dragOverItems.forEach(item => item.classList.remove('drag-over'));
    }

    // File Operation Methods
    async moveItem(sourceNode, targetNode) {
        try {
            const sourcePath = sourceNode.path;
            const targetPath = targetNode.path + '/' + sourceNode.name;

            await invoke('move_file', {
                source: sourcePath,
                target: targetPath
            });

            this.refresh();
        } catch (error) {
            console.error('Failed to move item:', error);
            this.showError('Failed to move item: ' + error.message);
        }
    }

    async deleteItem(node) {
        const confirmed = confirm(`Are you sure you want to delete "${node.name}"?`);
        if (!confirmed) return;

        try {
            await invoke('delete_file', { path: node.path });
            this.refresh();
        } catch (error) {
            console.error('Failed to delete item:', error);
            this.showError('Failed to delete item: ' + error.message);
        }
    }

    cutItem(node) {
        this.clipboard = node;
        this.clipboardOperation = 'cut';
        this.showNotification(`Cut: ${node.name}`);
    }

    copyItem(node) {
        this.clipboard = node;
        this.clipboardOperation = 'copy';
        this.showNotification(`Copied: ${node.name}`);
    }

    async pasteItem(targetNode) {
        if (!this.clipboard) return;

        try {
            const targetPath = targetNode.path + '/' + this.clipboard.name;

            if (this.clipboardOperation === 'cut') {
                await invoke('move_file', {
                    source: this.clipboard.path,
                    target: targetPath
                });
                this.showNotification(`Moved: ${this.clipboard.name}`);
            } else {
                await invoke('copy_file', {
                    source: this.clipboard.path,
                    target: targetPath
                });
                this.showNotification(`Copied: ${this.clipboard.name}`);
            }

            this.clipboard = null;
            this.clipboardOperation = null;
            this.refresh();
        } catch (error) {
            console.error('Failed to paste item:', error);
            this.showError('Failed to paste item: ' + error.message);
        }
    }

    async createNewFile(parentPath) {
        const fileName = prompt('Enter file name:');
        if (!fileName) return;

        try {
            const filePath = parentPath + '/' + fileName;
            await invoke('create_file', { path: filePath, content: '' });
            this.refresh();
            this.showNotification(`Created file: ${fileName}`);
        } catch (error) {
            console.error('Failed to create file:', error);
            this.showError('Failed to create file: ' + error.message);
        }
    }

    async createNewFolder(parentPath) {
        const folderName = prompt('Enter folder name:');
        if (!folderName) return;

        try {
            const folderPath = parentPath + '/' + folderName;
            await invoke('create_directory', { path: folderPath });
            this.refresh();
            this.showNotification(`Created folder: ${folderName}`);
        } catch (error) {
            console.error('Failed to create folder:', error);
            this.showError('Failed to create folder: ' + error.message);
        }
    }

    startRename(li, node) {
        if (this.renamingItem) return;

        this.renamingItem = { li, node };
        const label = li.querySelector('.file-tree-label');
        const originalText = label.textContent;

        const input = document.createElement('input');
        input.type = 'text';
        input.value = originalText;
        input.className = 'file-tree-rename-input';

        input.addEventListener('blur', () => this.finishRename(input, originalText));
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.finishRename(input, originalText);
            } else if (e.key === 'Escape') {
                this.cancelRename(input, originalText);
            }
        });

        label.replaceWith(input);
        input.focus();
        input.select();
    }

    async finishRename(input, originalText) {
        const newName = input.value.trim();
        if (!newName || newName === originalText) {
            this.cancelRename(input, originalText);
            return;
        }

        try {
            const oldPath = this.renamingItem.node.path;
            const newPath = this.getParentPath(oldPath) + '/' + newName;

            await invoke('rename_file', {
                oldPath: oldPath,
                newPath: newPath
            });

            this.renamingItem = null;
            this.refresh();
            this.showNotification(`Renamed to: ${newName}`);
        } catch (error) {
            console.error('Failed to rename:', error);
            this.showError('Failed to rename: ' + error.message);
            this.cancelRename(input, originalText);
        }
    }

    cancelRename(input, originalText) {
        const label = document.createElement('span');
        label.className = 'file-tree-label';
        label.textContent = originalText;
        input.replaceWith(label);
        this.renamingItem = null;
    }

    getParentPath(path) {
        const parts = path.split('/');
        parts.pop();
        return parts.join('/') || '/';
    }

    showNotification(message) {
        // Use status bar for notifications
        if (window.rustCodeApp && window.rustCodeApp.statusBar) {
            window.rustCodeApp.statusBar.showNotification(message, 'info');
        } else {
            console.log('Notification:', message);
        }
    }

    showError(message) {
        // Use status bar for errors
        if (window.rustCodeApp && window.rustCodeApp.statusBar) {
            window.rustCodeApp.statusBar.showNotification(message, 'error');
        } else {
            console.error('Error:', message);
        }
    }

    refresh() {
        if (window.rustCodeApp && window.rustCodeApp.currentWorkspace) {
            this.loadDirectory(window.rustCodeApp.currentWorkspace);
        }
    }
}

import * as monaco from 'monaco-editor';

export class CodeIntelligenceManager {
    constructor(editorManager) {
        this.editorManager = editorManager;
        this.languageProviders = new Map();
        this.diagnostics = new Map();
        this.formatters = new Map();
        
        this.init();
    }

    init() {
        this.setupLanguageProviders();
        this.setupFormatters();
        this.setupDiagnostics();
        this.setupCodeActions();
    }

    setupLanguageProviders() {
        // JavaScript/TypeScript
        this.registerJavaScriptProviders();
        
        // Rust
        this.registerRustProviders();
        
        // Python
        this.registerPythonProviders();
        
        // HTML/CSS
        this.registerWebProviders();
        
        // JSON
        this.registerJsonProviders();
        
        // Markdown
        this.registerMarkdownProviders();
    }

    registerJavaScriptProviders() {
        const jsCompletionProvider = {
            provideCompletionItems: (model, position) => {
                const suggestions = [
                    {
                        label: 'console.log',
                        kind: monaco.languages.CompletionItemKind.Function,
                        insertText: 'console.log(${1:message});',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Log a message to the console'
                    },
                    {
                        label: 'function',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'function ${1:name}(${2:params}) {\n\t${3:// body}\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Function declaration'
                    },
                    {
                        label: 'if',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'if (${1:condition}) {\n\t${2:// body}\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'If statement'
                    },
                    {
                        label: 'for',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'for (let ${1:i} = 0; ${1:i} < ${2:array}.length; ${1:i}++) {\n\t${3:// body}\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'For loop'
                    },
                    {
                        label: 'try-catch',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'try {\n\t${1:// code}\n} catch (${2:error}) {\n\t${3:// handle error}\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Try-catch block'
                    }
                ];

                return { suggestions };
            }
        };

        monaco.languages.registerCompletionItemProvider('javascript', jsCompletionProvider);
        monaco.languages.registerCompletionItemProvider('typescript', jsCompletionProvider);
    }

    registerRustProviders() {
        const rustCompletionProvider = {
            provideCompletionItems: (model, position) => {
                const suggestions = [
                    {
                        label: 'println!',
                        kind: monaco.languages.CompletionItemKind.Function,
                        insertText: 'println!("${1:message}");',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Print a line to stdout'
                    },
                    {
                        label: 'fn',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'fn ${1:name}(${2:params}) -> ${3:ReturnType} {\n\t${4:// body}\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Function definition'
                    },
                    {
                        label: 'struct',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'struct ${1:Name} {\n\t${2:field}: ${3:Type},\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Struct definition'
                    },
                    {
                        label: 'impl',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'impl ${1:Type} {\n\t${2:// methods}\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Implementation block'
                    },
                    {
                        label: 'match',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'match ${1:expression} {\n\t${2:pattern} => ${3:result},\n\t_ => ${4:default},\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Match expression'
                    }
                ];

                return { suggestions };
            }
        };

        monaco.languages.registerCompletionItemProvider('rust', rustCompletionProvider);
    }

    registerPythonProviders() {
        const pythonCompletionProvider = {
            provideCompletionItems: (model, position) => {
                const suggestions = [
                    {
                        label: 'print',
                        kind: monaco.languages.CompletionItemKind.Function,
                        insertText: 'print(${1:message})',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Print to stdout'
                    },
                    {
                        label: 'def',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'def ${1:name}(${2:params}):\n\t${3:pass}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Function definition'
                    },
                    {
                        label: 'class',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'class ${1:Name}:\n\tdef __init__(self${2:, params}):\n\t\t${3:pass}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Class definition'
                    },
                    {
                        label: 'if',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'if ${1:condition}:\n\t${2:pass}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'If statement'
                    },
                    {
                        label: 'for',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'for ${1:item} in ${2:iterable}:\n\t${3:pass}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'For loop'
                    }
                ];

                return { suggestions };
            }
        };

        monaco.languages.registerCompletionItemProvider('python', pythonCompletionProvider);
    }

    registerWebProviders() {
        const htmlCompletionProvider = {
            provideCompletionItems: (model, position) => {
                const suggestions = [
                    {
                        label: 'div',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '<div${1: class="${2:className}"}>\n\t${3:content}\n</div>',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Div element'
                    },
                    {
                        label: 'button',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '<button${1: onclick="${2:function}"}>${3:text}</button>',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Button element'
                    },
                    {
                        label: 'input',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '<input type="${1:text}" ${2:placeholder="${3:placeholder}"} />',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Input element'
                    }
                ];

                return { suggestions };
            }
        };

        const cssCompletionProvider = {
            provideCompletionItems: (model, position) => {
                const suggestions = [
                    {
                        label: 'flexbox',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'display: flex;\njustify-content: ${1:center};\nalign-items: ${2:center};',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Flexbox layout'
                    },
                    {
                        label: 'grid',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: 'display: grid;\ngrid-template-columns: ${1:repeat(auto-fit, minmax(200px, 1fr))};\ngap: ${2:1rem};',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'CSS Grid layout'
                    }
                ];

                return { suggestions };
            }
        };

        monaco.languages.registerCompletionItemProvider('html', htmlCompletionProvider);
        monaco.languages.registerCompletionItemProvider('css', cssCompletionProvider);
    }

    registerJsonProviders() {
        const jsonCompletionProvider = {
            provideCompletionItems: (model, position) => {
                const suggestions = [
                    {
                        label: 'package.json',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '{\n\t"name": "${1:package-name}",\n\t"version": "${2:1.0.0}",\n\t"description": "${3:description}",\n\t"main": "${4:index.js}",\n\t"scripts": {\n\t\t"start": "${5:node index.js}"\n\t}\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Package.json template'
                    }
                ];

                return { suggestions };
            }
        };

        monaco.languages.registerCompletionItemProvider('json', jsonCompletionProvider);
    }

    registerMarkdownProviders() {
        const markdownCompletionProvider = {
            provideCompletionItems: (model, position) => {
                const suggestions = [
                    {
                        label: 'code block',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '```${1:language}\n${2:code}\n```',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Code block'
                    },
                    {
                        label: 'table',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '| ${1:Header 1} | ${2:Header 2} |\n|-------------|-------------|\n| ${3:Cell 1}  | ${4:Cell 2}  |',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Markdown table'
                    }
                ];

                return { suggestions };
            }
        };

        monaco.languages.registerCompletionItemProvider('markdown', markdownCompletionProvider);
    }

    setupFormatters() {
        // JavaScript/TypeScript formatter
        monaco.languages.registerDocumentFormattingEditProvider('javascript', {
            provideDocumentFormattingEdits: (model) => {
                return this.formatJavaScript(model);
            }
        });

        monaco.languages.registerDocumentFormattingEditProvider('typescript', {
            provideDocumentFormattingEdits: (model) => {
                return this.formatJavaScript(model);
            }
        });

        // JSON formatter
        monaco.languages.registerDocumentFormattingEditProvider('json', {
            provideDocumentFormattingEdits: (model) => {
                return this.formatJson(model);
            }
        });
    }

    formatJavaScript(model) {
        try {
            const content = model.getValue();
            // Basic JavaScript formatting
            const formatted = this.basicJavaScriptFormat(content);
            
            return [{
                range: model.getFullModelRange(),
                text: formatted
            }];
        } catch (error) {
            console.error('JavaScript formatting failed:', error);
            return [];
        }
    }

    basicJavaScriptFormat(code) {
        // Simple formatting rules
        let formatted = code
            .replace(/\s*{\s*/g, ' {\n')
            .replace(/;\s*}/g, ';\n}')
            .replace(/,\s*/g, ', ')
            .replace(/\s*=\s*/g, ' = ')
            .replace(/\s*\+\s*/g, ' + ')
            .replace(/\s*-\s*/g, ' - ')
            .replace(/\s*\*\s*/g, ' * ')
            .replace(/\s*\/\s*/g, ' / ');

        // Add proper indentation
        const lines = formatted.split('\n');
        let indentLevel = 0;
        const indentSize = 4;

        return lines.map(line => {
            const trimmed = line.trim();
            if (trimmed.includes('}')) indentLevel--;
            const indented = ' '.repeat(indentLevel * indentSize) + trimmed;
            if (trimmed.includes('{')) indentLevel++;
            return indented;
        }).join('\n');
    }

    formatJson(model) {
        try {
            const content = model.getValue();
            const parsed = JSON.parse(content);
            const formatted = JSON.stringify(parsed, null, 2);
            
            return [{
                range: model.getFullModelRange(),
                text: formatted
            }];
        } catch (error) {
            console.error('JSON formatting failed:', error);
            return [];
        }
    }

    setupDiagnostics() {
        // Set up basic syntax checking
        monaco.languages.onLanguage('javascript', () => {
            this.setupJavaScriptDiagnostics();
        });

        monaco.languages.onLanguage('json', () => {
            this.setupJsonDiagnostics();
        });
    }

    setupJavaScriptDiagnostics() {
        // Basic JavaScript syntax checking
        const checkSyntax = (model) => {
            const markers = [];
            const content = model.getValue();
            const lines = content.split('\n');

            lines.forEach((line, index) => {
                // Check for common issues
                if (line.includes('console.log') && !line.includes(';')) {
                    markers.push({
                        severity: monaco.MarkerSeverity.Warning,
                        startLineNumber: index + 1,
                        startColumn: 1,
                        endLineNumber: index + 1,
                        endColumn: line.length + 1,
                        message: 'Missing semicolon'
                    });
                }

                if (line.includes('var ')) {
                    markers.push({
                        severity: monaco.MarkerSeverity.Info,
                        startLineNumber: index + 1,
                        startColumn: line.indexOf('var') + 1,
                        endLineNumber: index + 1,
                        endColumn: line.indexOf('var') + 4,
                        message: 'Consider using let or const instead of var'
                    });
                }
            });

            monaco.editor.setModelMarkers(model, 'javascript', markers);
        };

        // Check syntax on model changes
        monaco.editor.onDidCreateModel((model) => {
            if (model.getLanguageId() === 'javascript') {
                checkSyntax(model);
                model.onDidChangeContent(() => {
                    setTimeout(() => checkSyntax(model), 500);
                });
            }
        });
    }

    setupJsonDiagnostics() {
        const checkJsonSyntax = (model) => {
            const markers = [];
            const content = model.getValue();

            try {
                JSON.parse(content);
            } catch (error) {
                markers.push({
                    severity: monaco.MarkerSeverity.Error,
                    startLineNumber: 1,
                    startColumn: 1,
                    endLineNumber: 1,
                    endColumn: 1,
                    message: `JSON Syntax Error: ${error.message}`
                });
            }

            monaco.editor.setModelMarkers(model, 'json', markers);
        };

        monaco.editor.onDidCreateModel((model) => {
            if (model.getLanguageId() === 'json') {
                checkJsonSyntax(model);
                model.onDidChangeContent(() => {
                    setTimeout(() => checkJsonSyntax(model), 300);
                });
            }
        });
    }

    setupCodeActions() {
        // Register code actions for quick fixes
        monaco.languages.registerCodeActionProvider('javascript', {
            provideCodeActions: (model, range, context) => {
                const actions = [];

                context.markers.forEach(marker => {
                    if (marker.message.includes('semicolon')) {
                        actions.push({
                            title: 'Add semicolon',
                            kind: 'quickfix',
                            edit: {
                                edits: [{
                                    resource: model.uri,
                                    edit: {
                                        range: {
                                            startLineNumber: marker.endLineNumber,
                                            startColumn: marker.endColumn,
                                            endLineNumber: marker.endLineNumber,
                                            endColumn: marker.endColumn
                                        },
                                        text: ';'
                                    }
                                }]
                            }
                        });
                    }

                    if (marker.message.includes('var')) {
                        actions.push({
                            title: 'Replace var with let',
                            kind: 'quickfix',
                            edit: {
                                edits: [{
                                    resource: model.uri,
                                    edit: {
                                        range: {
                                            startLineNumber: marker.startLineNumber,
                                            startColumn: marker.startColumn,
                                            endLineNumber: marker.endLineNumber,
                                            endColumn: marker.endColumn
                                        },
                                        text: 'let'
                                    }
                                }]
                            }
                        });
                    }
                });

                return {
                    actions: actions,
                    dispose: () => {}
                };
            }
        });
    }

    // Enhanced minimap configuration
    configureMinimap(editor) {
        editor.updateOptions({
            minimap: {
                enabled: true,
                side: 'right',
                showSlider: 'mouseover',
                renderCharacters: true,
                maxColumn: 120,
                scale: 1
            }
        });
    }

    // Enhanced folding configuration
    configureFolding(editor) {
        editor.updateOptions({
            folding: true,
            foldingStrategy: 'auto',
            foldingHighlight: true,
            unfoldOnClickAfterEndOfLine: true,
            showFoldingControls: 'mouseover'
        });
    }

    // Apply all enhancements to an editor
    enhanceEditor(editor) {
        this.configureMinimap(editor);
        this.configureFolding(editor);
        
        // Add additional editor enhancements
        editor.updateOptions({
            suggest: {
                showKeywords: true,
                showSnippets: true,
                showFunctions: true,
                showConstructors: true,
                showFields: true,
                showVariables: true,
                showClasses: true,
                showStructs: true,
                showInterfaces: true,
                showModules: true,
                showProperties: true,
                showEvents: true,
                showOperators: true,
                showUnits: true,
                showValues: true,
                showConstants: true,
                showEnums: true,
                showEnumMembers: true,
                showColors: true,
                showFiles: true,
                showReferences: true,
                showFolders: true,
                showTypeParameters: true
            },
            quickSuggestions: {
                other: true,
                comments: false,
                strings: false
            },
            suggestOnTriggerCharacters: true,
            acceptSuggestionOnEnter: 'on',
            acceptSuggestionOnCommitCharacter: true,
            snippetSuggestions: 'top',
            wordBasedSuggestions: true,
            parameterHints: {
                enabled: true,
                cycle: true
            },
            hover: {
                enabled: true,
                delay: 300,
                sticky: true
            }
        });
    }
}

import { createIcon } from '../icons/icon-library.js';

export class AgenticCodingManager {
    constructor(container, editorManager, statusBar) {
        this.container = container;
        this.editorManager = editorManager;
        this.statusBar = statusBar;
        this.apiKey = localStorage.getItem('gemini-api-key') || '';
        this.isEnabled = false;
        this.currentTask = null;
        this.taskQueue = [];
        this.agenticMode = false;

        this.init();
    }

    init() {
        this.createUI();
        this.setupEventListeners();
        this.loadSettings();
    }

    createUI() {
        this.container.innerHTML = `
            <div class="agentic-panel">
                <!-- Header Section -->
                <div class="agentic-header">
                    <div class="agentic-title">
                        ${createIcon('ai', 22, 'agentic-main-icon')}
                        <div class="title-content">
                            <h2>AI Assistant</h2>
                            <span class="subtitle">Powered by Gemini</span>
                        </div>
                    </div>
                    <div class="agentic-controls">
                        <button id="agentic-settings-btn" class="control-btn" title="Settings">
                            ${createIcon('settings', 18)}
                        </button>
                        <button id="agentic-toggle-btn" class="control-btn ${this.agenticMode ? 'active' : ''}" title="Toggle Full Agentic Mode">
                            ${createIcon('robot', 18)}
                        </button>
                        <button id="agentic-close-btn" class="control-btn close-btn" title="Close Panel">
                            ${createIcon('close', 18)}
                        </button>
                    </div>
                </div>

                <!-- Status Section -->
                <div class="agentic-status-section">
                    <div class="status-card">
                        <div class="status-indicator ${this.isEnabled ? 'active' : 'inactive'}">
                            <div class="status-dot"></div>
                            <div class="status-info">
                                <span class="status-label">${this.isEnabled ? 'Connected' : 'Disconnected'}</span>
                                <span class="status-detail">${this.isEnabled ? 'Ready to assist' : 'Configure API key'}</span>
                            </div>
                        </div>
                        <div class="agentic-mode-toggle">
                            <label class="toggle-switch">
                                <input type="checkbox" id="full-agentic-mode" ${this.agenticMode ? 'checked' : ''}>
                                <span class="toggle-slider"></span>
                                <span class="toggle-label">Full Agentic Mode</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Features Section -->
                <div class="agentic-features-section">
                    <div class="section-header">
                        <h3>AI Features</h3>
                        <span class="feature-count">5 available</span>
                    </div>
                    <div class="features-grid">
                        <button class="feature-card" data-feature="generate" title="Generate code from natural language">
                            <div class="feature-icon">
                                ${createIcon('codeGenerate', 28)}
                            </div>
                            <div class="feature-content">
                                <h4>Generate Code</h4>
                                <p>Create code from descriptions</p>
                            </div>
                        </button>
                        <button class="feature-card" data-feature="review" title="Analyze code quality and best practices">
                            <div class="feature-icon">
                                ${createIcon('codeReview', 28)}
                            </div>
                            <div class="feature-content">
                                <h4>Code Review</h4>
                                <p>Analyze quality & security</p>
                            </div>
                        </button>
                        <button class="feature-card" data-feature="fix" title="Detect and fix bugs automatically">
                            <div class="feature-icon">
                                ${createIcon('bugFix', 28)}
                            </div>
                            <div class="feature-content">
                                <h4>Fix Bugs</h4>
                                <p>Detect & resolve issues</p>
                            </div>
                        </button>
                        <button class="feature-card" data-feature="refactor" title="Improve code structure and performance">
                            <div class="feature-icon">
                                ${createIcon('refactor', 28)}
                            </div>
                            <div class="feature-content">
                                <h4>Refactor</h4>
                                <p>Optimize & restructure</p>
                            </div>
                        </button>
                        <button class="feature-card" data-feature="document" title="Generate comprehensive documentation">
                            <div class="feature-icon">
                                ${createIcon('document', 28)}
                            </div>
                            <div class="feature-content">
                                <h4>Document</h4>
                                <p>Auto-generate docs</p>
                            </div>
                        </button>
                    </div>
                </div>

                <!-- Chat Section -->
                <div class="agentic-chat-section">
                    <div class="section-header">
                        <h3>AI Chat</h3>
                        <div class="chat-controls">
                            <button id="clear-chat-btn" class="control-btn" title="Clear Chat">
                                ${createIcon('trash', 16)}
                            </button>
                        </div>
                    </div>
                    <div class="chat-container">
                        <div class="chat-messages" id="agentic-messages">
                            <div class="welcome-message">
                                <div class="welcome-icon">
                                    ${createIcon('sparkles', 24)}
                                </div>
                                <div class="welcome-content">
                                    <h4>Welcome to AI Assistant</h4>
                                    <p>Ask me anything about your code or use the feature buttons above.</p>
                                </div>
                            </div>
                        </div>
                        <div class="chat-input-container">
                            <div class="input-wrapper">
                                <input type="text" id="agentic-input" placeholder="Ask me anything about your code..." disabled>
                                <button id="agentic-send-btn" class="send-btn" disabled>
                                    ${createIcon('chevronRight', 20)}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tasks Section -->
                <div class="agentic-tasks-section">
                    <div class="section-header">
                        <h3>Active Tasks</h3>
                        <div class="task-controls">
                            <span class="task-count" id="task-count">0 tasks</span>
                            <button id="clear-tasks-btn" class="control-btn" title="Clear All Tasks">
                                ${createIcon('trash', 16)}
                            </button>
                        </div>
                    </div>
                    <div class="tasks-container">
                        <div class="tasks-list" id="agentic-tasks-list">
                            <div class="no-tasks-message">
                                <div class="no-tasks-icon">
                                    ${createIcon('check', 24)}
                                </div>
                                <p>No active tasks</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

                <!-- Settings Modal -->
                <div class="agentic-settings-modal hidden" id="agentic-settings-modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Agentic Coding Settings</h3>
                            <button class="modal-close" id="close-settings-modal">
                                ${createIcon('close', 16)}
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="setting-group">
                                <label for="gemini-api-key">Gemini API Key:</label>
                                <input type="password" id="gemini-api-key" placeholder="Enter your Gemini API key" value="${this.apiKey}">
                                <small>Get your API key from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></small>
                            </div>
                            <div class="setting-group">
                                <label for="agentic-model">Model:</label>
                                <select id="agentic-model">
                                    <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                                    <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                                </select>
                            </div>
                            <div class="setting-group">
                                <label for="agentic-temperature">Creativity Level:</label>
                                <input type="range" id="agentic-temperature" min="0" max="1" step="0.1" value="0.7">
                                <span class="range-value">0.7</span>
                            </div>
                            <div class="setting-group">
                                <label>
                                    <input type="checkbox" id="auto-apply-changes">
                                    Auto-apply code changes
                                </label>
                            </div>
                            <div class="setting-group">
                                <label>
                                    <input type="checkbox" id="continuous-mode">
                                    Continuous improvement mode
                                </label>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button id="save-agentic-settings" class="primary-btn">Save Settings</button>
                            <button id="test-api-connection" class="secondary-btn">Test Connection</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Settings modal
        document.getElementById('agentic-settings-btn').addEventListener('click', () => {
            this.showSettings();
        });

        document.getElementById('close-settings-modal').addEventListener('click', () => {
            this.hideSettings();
        });

        // Agentic mode toggle
        document.getElementById('agentic-toggle-btn').addEventListener('click', () => {
            this.toggleAgenticMode();
        });

        // Close panel button
        document.getElementById('agentic-close-btn').addEventListener('click', () => {
            this.closePanel();
        });

        document.getElementById('full-agentic-mode').addEventListener('change', (e) => {
            this.agenticMode = e.target.checked;
            this.saveSettings();
            if (this.agenticMode) {
                this.startFullAgenticMode();
            } else {
                this.stopFullAgenticMode();
            }
        });

        // Feature buttons
        document.querySelectorAll('.feature-card').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const feature = e.currentTarget.dataset.feature;
                this.executeFeature(feature);
            });
        });

        // Clear chat button
        document.getElementById('clear-chat-btn').addEventListener('click', () => {
            this.clearChat();
        });

        // Chat input
        const chatInput = document.getElementById('agentic-input');
        const sendBtn = document.getElementById('agentic-send-btn');

        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        sendBtn.addEventListener('click', () => {
            this.sendMessage();
        });

        // Settings
        document.getElementById('save-agentic-settings').addEventListener('click', () => {
            this.saveSettings();
        });

        document.getElementById('test-api-connection').addEventListener('click', () => {
            this.testApiConnection();
        });

        document.getElementById('gemini-api-key').addEventListener('input', (e) => {
            this.apiKey = e.target.value;
            this.updateUIState();
        });

        document.getElementById('agentic-temperature').addEventListener('input', (e) => {
            document.querySelector('.range-value').textContent = e.target.value;
        });

        // Clear tasks
        document.getElementById('clear-tasks-btn').addEventListener('click', () => {
            this.clearAllTasks();
        });
    }

    loadSettings() {
        const settings = JSON.parse(localStorage.getItem('agentic-settings') || '{}');

        if (settings.model) {
            document.getElementById('agentic-model').value = settings.model;
        }
        if (settings.temperature !== undefined) {
            document.getElementById('agentic-temperature').value = settings.temperature;
            document.querySelector('.range-value').textContent = settings.temperature;
        }
        if (settings.autoApply !== undefined) {
            document.getElementById('auto-apply-changes').checked = settings.autoApply;
        }
        if (settings.continuousMode !== undefined) {
            document.getElementById('continuous-mode').checked = settings.continuousMode;
        }
        if (settings.agenticMode !== undefined) {
            this.agenticMode = settings.agenticMode;
            document.getElementById('full-agentic-mode').checked = this.agenticMode;
        }

        this.updateUIState();
    }

    saveSettings() {
        const settings = {
            model: document.getElementById('agentic-model').value,
            temperature: parseFloat(document.getElementById('agentic-temperature').value),
            autoApply: document.getElementById('auto-apply-changes').checked,
            continuousMode: document.getElementById('continuous-mode').checked,
            agenticMode: this.agenticMode
        };

        localStorage.setItem('agentic-settings', JSON.stringify(settings));
        localStorage.setItem('gemini-api-key', this.apiKey);

        this.updateUIState();
        this.hideSettings();
        this.statusBar.showNotification('Settings saved successfully', 'success');
    }

    updateUIState() {
        this.isEnabled = this.apiKey.length > 0;

        // Update status indicator
        const statusIndicator = document.querySelector('.status-indicator');
        const statusLabel = document.querySelector('.status-label');
        const statusDetail = document.querySelector('.status-detail');

        if (statusIndicator) {
            statusIndicator.className = `status-indicator ${this.isEnabled ? 'active' : 'inactive'}`;
        }
        if (statusLabel) {
            statusLabel.textContent = this.isEnabled ? 'Connected' : 'Disconnected';
        }
        if (statusDetail) {
            statusDetail.textContent = this.isEnabled ? 'Ready to assist' : 'Configure API key';
        }

        // Update input states
        const chatInput = document.getElementById('agentic-input');
        const sendBtn = document.getElementById('agentic-send-btn');

        if (chatInput) {
            chatInput.disabled = !this.isEnabled;
            // Update placeholder text
            if (this.isEnabled) {
                chatInput.placeholder = "Ask me anything about your code...";
            } else {
                chatInput.placeholder = "Configure API key in settings to enable AI features";
            }
        }

        if (sendBtn) {
            sendBtn.disabled = !this.isEnabled;
        }

        // Update feature buttons
        document.querySelectorAll('.feature-card').forEach(btn => {
            btn.disabled = !this.isEnabled;
            if (this.isEnabled) {
                btn.classList.remove('disabled');
            } else {
                btn.classList.add('disabled');
            }
        });

        // Update toggle button state
        const toggleBtn = document.getElementById('agentic-toggle-btn');
        if (toggleBtn) {
            if (this.agenticMode) {
                toggleBtn.classList.add('active');
            } else {
                toggleBtn.classList.remove('active');
            }
        }
    }

    showSettings() {
        document.getElementById('agentic-settings-modal').classList.remove('hidden');
    }

    hideSettings() {
        document.getElementById('agentic-settings-modal').classList.add('hidden');
    }

    toggleAgenticMode() {
        this.agenticMode = !this.agenticMode;
        document.getElementById('full-agentic-mode').checked = this.agenticMode;
        this.saveSettings();

        if (this.agenticMode) {
            this.startFullAgenticMode();
        } else {
            this.stopFullAgenticMode();
        }
    }

    startFullAgenticMode() {
        this.addMessage('system', 'Full Agentic Mode activated. I will continuously analyze and improve your code.');
        this.statusBar.showNotification('Full Agentic Mode activated', 'success');

        // Start continuous monitoring
        this.startContinuousMonitoring();
    }

    stopFullAgenticMode() {
        this.addMessage('system', 'Full Agentic Mode deactivated.');
        this.statusBar.showNotification('Full Agentic Mode deactivated', 'info');

        // Stop continuous monitoring
        this.stopContinuousMonitoring();
    }

    startContinuousMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }

        this.monitoringInterval = setInterval(() => {
            if (this.agenticMode && this.isEnabled) {
                this.analyzeCurrentCode();
            }
        }, 10000); // Check every 10 seconds
    }

    stopContinuousMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
    }

    async analyzeCurrentCode() {
        if (!this.editorManager.editor) return;

        const code = this.editorManager.editor.getValue();
        if (!code || code.trim().length === 0) return;

        const language = this.editorManager.currentLanguage || 'javascript';

        try {
            const analysis = await this.callGeminiAPI(`
                Analyze this ${language} code for potential improvements, bugs, or optimizations.
                Provide a brief summary of findings. Only respond if there are actionable suggestions.

                Code:
                \`\`\`${language}
                ${code}
                \`\`\`
            `);

            if (analysis && analysis.trim().length > 0) {
                this.addMessage('assistant', `Code Analysis: ${analysis}`);

                if (document.getElementById('auto-apply-changes').checked) {
                    this.suggestImprovements(code, language);
                }
            }
        } catch (error) {
            console.error('Error analyzing code:', error);
        }
    }

    async executeFeature(feature) {
        if (!this.isEnabled) {
            this.statusBar.showNotification('Please configure API key first', 'warning');
            return;
        }

        const currentCode = this.editorManager.editor ? this.editorManager.editor.getValue() : '';
        const language = this.editorManager.currentLanguage || 'javascript';

        this.addTask(feature, 'pending');

        try {
            switch (feature) {
                case 'generate':
                    await this.generateCode();
                    break;
                case 'review':
                    await this.reviewCode(currentCode, language);
                    break;
                case 'fix':
                    await this.fixBugs(currentCode, language);
                    break;
                case 'refactor':
                    await this.refactorCode(currentCode, language);
                    break;
                case 'document':
                    await this.generateDocumentation(currentCode, language);
                    break;
            }

            this.updateTaskStatus(feature, 'completed');
        } catch (error) {
            this.updateTaskStatus(feature, 'failed');
            this.statusBar.showNotification(`Feature ${feature} failed: ${error.message}`, 'error');
        }
    }

    async generateCode() {
        const prompt = prompt('What code would you like me to generate?');
        if (!prompt) return;

        this.addMessage('user', `Generate: ${prompt}`);

        const response = await this.callGeminiAPI(`
            Generate clean, well-commented code for: ${prompt}

            Requirements:
            - Use modern best practices
            - Include error handling
            - Add helpful comments
            - Make it production-ready

            Respond with only the code, no explanations.
        `);

        this.addMessage('assistant', 'Generated code:', response);

        if (this.editorManager.editor) {
            const currentCode = this.editorManager.editor.getValue();
            if (currentCode.trim() === '') {
                this.editorManager.editor.setValue(response);
            } else {
                this.editorManager.editor.setValue(currentCode + '\n\n' + response);
            }
        }
    }

    async reviewCode(code, language) {
        if (!code || code.trim() === '') {
            this.addMessage('assistant', 'No code to review. Please write some code first.');
            return;
        }

        this.addMessage('user', 'Reviewing current code...');

        const response = await this.callGeminiAPI(`
            Perform a comprehensive code review of this ${language} code:

            \`\`\`${language}
            ${code}
            \`\`\`

            Please analyze:
            1. Code quality and best practices
            2. Potential bugs or issues
            3. Performance optimizations
            4. Security considerations
            5. Maintainability improvements

            Provide specific, actionable feedback.
        `);

        this.addMessage('assistant', 'Code Review Results:', response);
    }

    async fixBugs(code, language) {
        if (!code || code.trim() === '') {
            this.addMessage('assistant', 'No code to analyze for bugs.');
            return;
        }

        this.addMessage('user', 'Analyzing code for bugs...');

        const response = await this.callGeminiAPI(`
            Analyze this ${language} code for bugs and provide fixes:

            \`\`\`${language}
            ${code}
            \`\`\`

            Please:
            1. Identify any bugs or potential issues
            2. Provide the corrected code
            3. Explain what was fixed

            If no bugs are found, suggest defensive programming improvements.
        `);

        this.addMessage('assistant', 'Bug Analysis & Fixes:', response);

        // Extract and apply fixes if auto-apply is enabled
        if (document.getElementById('auto-apply-changes').checked) {
            this.extractAndApplyCodeFixes(response, language);
        }
    }

    async refactorCode(code, language) {
        if (!code || code.trim() === '') {
            this.addMessage('assistant', 'No code to refactor.');
            return;
        }

        this.addMessage('user', 'Refactoring current code...');

        const response = await this.callGeminiAPI(`
            Refactor this ${language} code to improve readability, maintainability, and performance:

            \`\`\`${language}
            ${code}
            \`\`\`

            Please:
            1. Improve code structure and organization
            2. Extract reusable functions/methods
            3. Optimize performance where possible
            4. Follow modern ${language} best practices
            5. Maintain the same functionality

            Provide the refactored code with explanations of changes.
        `);

        this.addMessage('assistant', 'Refactored Code:', response);

        if (document.getElementById('auto-apply-changes').checked) {
            this.extractAndApplyCodeFixes(response, language);
        }
    }

    async generateDocumentation(code, language) {
        if (!code || code.trim() === '') {
            this.addMessage('assistant', 'No code to document.');
            return;
        }

        this.addMessage('user', 'Generating documentation...');

        const response = await this.callGeminiAPI(`
            Generate comprehensive documentation for this ${language} code:

            \`\`\`${language}
            ${code}
            \`\`\`

            Please provide:
            1. Function/method documentation with parameters and return values
            2. Class documentation if applicable
            3. Usage examples
            4. README section if this is a complete module
            5. JSDoc/docstring format appropriate for ${language}
        `);

        this.addMessage('assistant', 'Generated Documentation:', response);
    }

    async sendMessage() {
        const input = document.getElementById('agentic-input');
        const message = input.value.trim();

        if (!message) return;

        input.value = '';
        this.addMessage('user', message);

        try {
            const currentCode = this.editorManager.editor ? this.editorManager.editor.getValue() : '';
            const language = this.editorManager.currentLanguage || 'javascript';

            let prompt = message;
            if (currentCode) {
                prompt += `\n\nCurrent code context:\n\`\`\`${language}\n${currentCode}\n\`\`\``;
            }

            const response = await this.callGeminiAPI(prompt);
            this.addMessage('assistant', response);

            // Check if response contains code and offer to apply it
            if (response.includes('```') && document.getElementById('auto-apply-changes').checked) {
                this.extractAndApplyCodeFixes(response, language);
            }
        } catch (error) {
            this.addMessage('assistant', `Error: ${error.message}`);
        }
    }

    async callGeminiAPI(prompt) {
        if (!this.apiKey) {
            throw new Error('API key not configured');
        }

        const model = document.getElementById('agentic-model').value;
        const temperature = parseFloat(document.getElementById('agentic-temperature').value);

        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${this.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: temperature,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 8192,
                }
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error?.message || 'API request failed');
        }

        const data = await response.json();
        return data.candidates[0]?.content?.parts[0]?.text || 'No response generated';
    }

    async testApiConnection() {
        if (!this.apiKey) {
            this.statusBar.showNotification('Please enter an API key first', 'warning');
            return;
        }

        try {
            const response = await this.callGeminiAPI('Hello! Please respond with "API connection successful" to test the connection.');

            if (response.toLowerCase().includes('successful') || response.toLowerCase().includes('hello')) {
                this.statusBar.showNotification('API connection successful!', 'success');
            } else {
                this.statusBar.showNotification('API connected but unexpected response', 'warning');
            }
        } catch (error) {
            this.statusBar.showNotification(`API connection failed: ${error.message}`, 'error');
        }
    }

    addMessage(type, content, codeContent = null) {
        const messagesContainer = document.getElementById('agentic-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${type}`;

        const timestamp = new Date().toLocaleTimeString();

        let iconName = 'user';
        if (type === 'assistant') iconName = 'ai';
        if (type === 'system') iconName = 'robot';

        messageDiv.innerHTML = `
            <div class="message-header">
                ${createIcon(iconName, 16, 'message-icon')}
                <span class="message-type">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
                <span class="message-time">${timestamp}</span>
            </div>
            <div class="message-content">
                ${this.formatMessage(content)}
                ${codeContent ? `<pre><code>${codeContent}</code></pre>` : ''}
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    formatMessage(content) {
        // Convert markdown-style code blocks to HTML
        return content
            .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>')
            .replace(/`([^`]+)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    extractAndApplyCodeFixes(response, language) {
        const codeBlocks = response.match(/```[\w]*\n([\s\S]*?)```/g);

        if (codeBlocks && codeBlocks.length > 0) {
            const code = codeBlocks[0].replace(/```[\w]*\n/, '').replace(/```$/, '');

            if (this.editorManager.editor) {
                const confirmed = confirm('Apply the suggested code changes?');
                if (confirmed) {
                    this.editorManager.editor.setValue(code);
                    this.statusBar.showNotification('Code changes applied', 'success');
                }
            }
        }
    }

    addTask(name, status) {
        const task = {
            id: Date.now(),
            name: name.charAt(0).toUpperCase() + name.slice(1),
            status: status,
            timestamp: new Date().toLocaleTimeString()
        };

        this.taskQueue.push(task);
        this.updateTasksList();
    }

    updateTaskStatus(name, status) {
        const task = this.taskQueue.find(t => t.name.toLowerCase() === name);
        if (task) {
            task.status = status;
            this.updateTasksList();
        }
    }

    updateTasksList() {
        const tasksList = document.getElementById('agentic-tasks-list');
        const taskCount = document.getElementById('task-count');

        // Update task count
        if (taskCount) {
            const count = this.taskQueue.length;
            taskCount.textContent = count === 0 ? '0 tasks' : count === 1 ? '1 task' : `${count} tasks`;
        }

        if (this.taskQueue.length === 0) {
            tasksList.innerHTML = `
                <div class="no-tasks-message">
                    <div class="no-tasks-icon">
                        ${createIcon('check', 24)}
                    </div>
                    <p>No active tasks</p>
                </div>
            `;
            return;
        }

        tasksList.innerHTML = this.taskQueue.map(task => `
            <div class="task-item ${task.status}">
                <div class="task-icon">
                    ${this.getStatusIcon(task.status)}
                </div>
                <div class="task-content">
                    <div class="task-name">${task.name}</div>
                    <div class="task-meta">
                        <span class="task-time">${task.timestamp}</span>
                        <span class="task-status-text">${task.status}</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    clearChat() {
        const messagesContainer = document.getElementById('agentic-messages');
        if (messagesContainer) {
            messagesContainer.innerHTML = `
                <div class="welcome-message">
                    <div class="welcome-icon">
                        ${createIcon('sparkles', 24)}
                    </div>
                    <div class="welcome-content">
                        <h4>Welcome to AI Assistant</h4>
                        <p>Ask me anything about your code or use the feature buttons above.</p>
                    </div>
                </div>
            `;
        }
    }

    getStatusIcon(status) {
        switch (status) {
            case 'pending': return createIcon('spinner', 14, 'loading');
            case 'completed': return createIcon('check', 14, 'success');
            case 'failed': return createIcon('error', 14, 'error');
            default: return createIcon('info', 14);
        }
    }

    clearAllTasks() {
        this.taskQueue = [];
        this.updateTasksList();
    }

    async suggestImprovements(code, language) {
        try {
            const suggestions = await this.callGeminiAPI(`
                Suggest specific improvements for this ${language} code. Be concise and actionable:

                \`\`\`${language}
                ${code}
                \`\`\`
            `);

            this.addMessage('assistant', `Suggestions: ${suggestions}`);
        } catch (error) {
            console.error('Error getting suggestions:', error);
        }
    }

    closePanel() {
        // Hide the panel by adding hidden class and triggering the main app's toggle
        this.container.classList.add('hidden');
        this.container.style.display = 'none';

        // If there's a global toggle function, call it
        if (window.rustCodeApp && window.rustCodeApp.toggleAgenticPanel) {
            // Don't call toggle as it would show the panel again
            // Just ensure it's hidden
        }
    }

    destroy() {
        this.stopContinuousMonitoring();
    }
}

import { createIcon } from '../icons/icon-library.js';

export class AgenticCodingManager {
    constructor(container, editorManager, statusBar) {
        this.container = container;
        this.editorManager = editorManager;
        this.statusBar = statusBar;
        this.apiKey = localStorage.getItem('gemini-api-key') || '';
        this.isEnabled = false;
        this.currentTask = null;
        this.taskQueue = [];
        this.agenticMode = false;

        this.init();
    }

    init() {
        this.createUI();
        this.setupEventListeners();
        this.loadSettings();
    }

    createUI() {
        this.container.innerHTML = `
            <div class="agentic-panel">
                <!-- Header Section -->
                <div class="agentic-header">
                    <div class="agentic-title">
                        ${createIcon('ai', 22, 'agentic-main-icon')}
                        <div class="title-content">
                            <h2>AI Assistant</h2>
                            <span class="subtitle">Powered by Gemini</span>
                        </div>
                    </div>
                    <div class="agentic-controls">
                        <button id="agentic-settings-btn" class="control-btn" title="Settings">
                            ${createIcon('settings', 18)}
                        </button>
                        <button id="agentic-toggle-btn" class="control-btn ${this.agenticMode ? 'active' : ''}" title="Toggle Full Agentic Mode">
                            ${createIcon('robot', 18)}
                        </button>
                        <button id="agentic-close-btn" class="control-btn close-btn" title="Close Panel">
                            ${createIcon('close', 18)}
                        </button>
                    </div>
                </div>

                <!-- Agentic Mode Toggle -->
                <div class="agentic-mode-section">
                    <label class="toggle-switch">
                        <input type="checkbox" id="full-agentic-mode" ${this.agenticMode ? 'checked' : ''}>
                        <span class="toggle-slider"></span>
                        <span class="toggle-label">Full Agentic Mode</span>
                    </label>
                </div>

                <!-- Chat Section -->
                <div class="agentic-chat-section">
                    <div class="chat-container">
                        <div class="chat-messages" id="agentic-messages">
                            <div class="welcome-message">
                                <div class="welcome-icon">
                                    ${createIcon('sparkles', 24)}
                                </div>
                                <div class="welcome-content">
                                    <h4>Welcome to AI Assistant</h4>
                                    <p>I can help you with code generation, review, debugging, refactoring, and documentation. Just ask me naturally!</p>
                                    ${!this.isEnabled ? '<p class="config-notice">Configure your API key in settings to get started.</p>' : ''}
                                </div>
                            </div>
                        </div>
                        <div class="chat-input-container">
                            <div class="input-wrapper">
                                <input type="text" id="agentic-input" placeholder="${this.isEnabled ? 'Ask me anything about your code...' : 'Configure API key in settings to enable AI features'}" ${!this.isEnabled ? 'disabled' : ''}>
                                <button id="agentic-send-btn" class="send-btn" ${!this.isEnabled ? 'disabled' : ''}>
                                    ${createIcon('chevronRight', 20)}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

                <!-- Settings Modal -->
                <div class="agentic-settings-modal hidden" id="agentic-settings-modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Agentic Coding Settings</h3>
                            <button class="modal-close" id="close-settings-modal">
                                ${createIcon('close', 16)}
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="setting-group">
                                <label for="gemini-api-key">Gemini API Key:</label>
                                <input type="password" id="gemini-api-key" placeholder="Enter your Gemini API key" value="${this.apiKey}">
                                <small>Get your API key from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></small>
                            </div>
                            <div class="setting-group">
                                <label for="agentic-model">Model:</label>
                                <select id="agentic-model">
                                    <option value="gemini-2.0-flash-exp">Gemini 2.0 Flash (Experimental)</option>
                                    <option value="gemini-exp-1206">Gemini 2.5 Flash (Experimental)</option>
                                </select>
                            </div>
                            <div class="setting-group">
                                <label for="agentic-temperature">Creativity Level:</label>
                                <input type="range" id="agentic-temperature" min="0" max="1" step="0.1" value="0.7">
                                <span class="range-value">0.7</span>
                            </div>
                            <div class="setting-group">
                                <label>
                                    <input type="checkbox" id="auto-apply-changes">
                                    Auto-apply code changes
                                </label>
                            </div>
                            <div class="setting-group">
                                <label>
                                    <input type="checkbox" id="continuous-mode">
                                    Continuous improvement mode
                                </label>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button id="save-agentic-settings" class="primary-btn">Save Settings</button>
                            <button id="test-api-connection" class="secondary-btn">Test Connection</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Settings modal
        document.getElementById('agentic-settings-btn').addEventListener('click', () => {
            this.showSettings();
        });

        document.getElementById('close-settings-modal').addEventListener('click', () => {
            this.hideSettings();
        });

        // Agentic mode toggle
        document.getElementById('agentic-toggle-btn').addEventListener('click', () => {
            this.toggleAgenticMode();
        });

        // Close panel button
        document.getElementById('agentic-close-btn').addEventListener('click', () => {
            this.closePanel();
        });

        document.getElementById('full-agentic-mode').addEventListener('change', (e) => {
            this.agenticMode = e.target.checked;
            this.saveSettings();
            if (this.agenticMode) {
                this.startFullAgenticMode();
            } else {
                this.stopFullAgenticMode();
            }
        });

        // No feature cards in streamlined interface

        // Chat input
        const chatInput = document.getElementById('agentic-input');
        const sendBtn = document.getElementById('agentic-send-btn');

        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        sendBtn.addEventListener('click', () => {
            this.sendMessage();
        });

        // Settings
        document.getElementById('save-agentic-settings').addEventListener('click', () => {
            this.saveSettings();
        });

        document.getElementById('test-api-connection').addEventListener('click', () => {
            this.testApiConnection();
        });

        document.getElementById('gemini-api-key').addEventListener('input', (e) => {
            this.apiKey = e.target.value;
            this.updateUIState();
        });

        document.getElementById('agentic-temperature').addEventListener('input', (e) => {
            document.querySelector('.range-value').textContent = e.target.value;
        });

        // No task management in streamlined interface
    }

    loadSettings() {
        const settings = JSON.parse(localStorage.getItem('agentic-settings') || '{}');

        if (settings.model) {
            document.getElementById('agentic-model').value = settings.model;
        }
        if (settings.temperature !== undefined) {
            document.getElementById('agentic-temperature').value = settings.temperature;
            document.querySelector('.range-value').textContent = settings.temperature;
        }
        if (settings.autoApply !== undefined) {
            document.getElementById('auto-apply-changes').checked = settings.autoApply;
        }
        if (settings.continuousMode !== undefined) {
            document.getElementById('continuous-mode').checked = settings.continuousMode;
        }
        if (settings.agenticMode !== undefined) {
            this.agenticMode = settings.agenticMode;
            document.getElementById('full-agentic-mode').checked = this.agenticMode;
        }

        this.updateUIState();
    }

    saveSettings() {
        const settings = {
            model: document.getElementById('agentic-model').value,
            temperature: parseFloat(document.getElementById('agentic-temperature').value),
            autoApply: document.getElementById('auto-apply-changes').checked,
            continuousMode: document.getElementById('continuous-mode').checked,
            agenticMode: this.agenticMode
        };

        localStorage.setItem('agentic-settings', JSON.stringify(settings));
        localStorage.setItem('gemini-api-key', this.apiKey);

        this.updateUIState();
        this.hideSettings();
        this.statusBar.showNotification('Settings saved successfully', 'success');
    }

    updateUIState() {
        this.isEnabled = this.apiKey.length > 0;

        // Update input states
        const chatInput = document.getElementById('agentic-input');
        const sendBtn = document.getElementById('agentic-send-btn');

        if (chatInput) {
            chatInput.disabled = !this.isEnabled;
            // Update placeholder text
            if (this.isEnabled) {
                chatInput.placeholder = "Ask me anything about your code...";
            } else {
                chatInput.placeholder = "Configure API key in settings to enable AI features";
            }
        }

        if (sendBtn) {
            sendBtn.disabled = !this.isEnabled;
        }

        // Update toggle button state
        const toggleBtn = document.getElementById('agentic-toggle-btn');
        if (toggleBtn) {
            if (this.agenticMode) {
                toggleBtn.classList.add('active');
            } else {
                toggleBtn.classList.remove('active');
            }
        }

        // Update welcome message if API key status changed
        this.updateWelcomeMessage();
    }

    updateWelcomeMessage() {
        const welcomeContent = document.querySelector('.welcome-content');
        if (welcomeContent) {
            welcomeContent.innerHTML = `
                <h4>Welcome to AI Assistant</h4>
                <p>I can help you with code generation, review, debugging, refactoring, and documentation. Just ask me naturally!</p>
                ${!this.isEnabled ? '<p class="config-notice">Configure your API key in settings to get started.</p>' : ''}
            `;
        }
    }

    showSettings() {
        document.getElementById('agentic-settings-modal').classList.remove('hidden');
    }

    hideSettings() {
        document.getElementById('agentic-settings-modal').classList.add('hidden');
    }

    toggleAgenticMode() {
        this.agenticMode = !this.agenticMode;
        document.getElementById('full-agentic-mode').checked = this.agenticMode;
        this.saveSettings();

        if (this.agenticMode) {
            this.startFullAgenticMode();
        } else {
            this.stopFullAgenticMode();
        }
    }

    startFullAgenticMode() {
        this.addMessage('system', 'Full Agentic Mode activated. I will continuously analyze and improve your code.');
        this.statusBar.showNotification('Full Agentic Mode activated', 'success');

        // Start continuous monitoring
        this.startContinuousMonitoring();
    }

    stopFullAgenticMode() {
        this.addMessage('system', 'Full Agentic Mode deactivated.');
        this.statusBar.showNotification('Full Agentic Mode deactivated', 'info');

        // Stop continuous monitoring
        this.stopContinuousMonitoring();
    }

    startContinuousMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }

        this.monitoringInterval = setInterval(() => {
            if (this.agenticMode && this.isEnabled) {
                this.analyzeCurrentCode();
            }
        }, 10000); // Check every 10 seconds
    }

    stopContinuousMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
    }

    async analyzeCurrentCode() {
        if (!this.editorManager.editor) return;

        const code = this.editorManager.editor.getValue();
        if (!code || code.trim().length === 0) return;

        const language = this.editorManager.currentLanguage || 'javascript';

        try {
            const analysis = await this.callGeminiAPI(`
                Analyze this ${language} code for potential improvements, bugs, or optimizations.
                Provide a brief summary of findings. Only respond if there are actionable suggestions.

                Code:
                \`\`\`${language}
                ${code}
                \`\`\`
            `);

            if (analysis && analysis.trim().length > 0) {
                this.addMessage('assistant', `Code Analysis: ${analysis}`);

                if (document.getElementById('auto-apply-changes').checked) {
                    this.suggestImprovements(code, language);
                }
            }
        } catch (error) {
            console.error('Error analyzing code:', error);
        }
    }

    // Feature execution now handled through conversational interface

    // Individual feature methods removed - all functionality now available through conversational interface

    async sendMessage() {
        const input = document.getElementById('agentic-input');
        const message = input.value.trim();

        if (!message) return;

        input.value = '';
        this.addMessage('user', message);

        try {
            const currentCode = this.editorManager.editor ? this.editorManager.editor.getValue() : '';
            const language = this.editorManager.currentLanguage || 'javascript';
            const fileName = this.getCurrentFileName();

            // Enhanced context-aware prompt
            let prompt = this.buildContextualPrompt(message, currentCode, language, fileName);

            const response = await this.callGeminiAPI(prompt);
            this.addMessage('assistant', response);

            // Check if response contains code and offer to apply it
            if (response.includes('```') && document.getElementById('auto-apply-changes').checked) {
                this.extractAndApplyCodeFixes(response, language);
            }

            // Proactive follow-up suggestions
            if (this.agenticMode) {
                setTimeout(() => this.offerProactiveHelp(message, currentCode, language), 2000);
            }
        } catch (error) {
            this.addMessage('assistant', `Error: ${error.message}`);
        }
    }

    buildContextualPrompt(userMessage, currentCode, language, fileName) {
        let prompt = `You are an expert coding assistant integrated with a code editor. The user is asking: "${userMessage}"

Context:
- Current file: ${fileName || 'Untitled'}
- Language: ${language}
- Editor has ${currentCode ? 'code content' : 'no content'}

Instructions:
- Provide helpful, actionable responses
- If generating code, use proper formatting with \`\`\`${language} blocks
- Consider the current code context when relevant
- Be conversational but precise
- Offer specific improvements when analyzing code
- Suggest best practices and optimizations

`;

        if (currentCode) {
            prompt += `\nCurrent code:\n\`\`\`${language}\n${currentCode}\n\`\`\`\n`;
        }

        prompt += `\nUser request: ${userMessage}`;

        return prompt;
    }

    getCurrentFileName() {
        // Get current file name from tab manager or editor state
        try {
            const activeTab = document.querySelector('.tab.active');
            return activeTab ? activeTab.textContent.trim() : null;
        } catch {
            return null;
        }
    }

    async offerProactiveHelp(lastMessage, currentCode, language) {
        if (!this.isEnabled || !currentCode) return;

        // Analyze if we should offer proactive suggestions
        const shouldSuggest = this.shouldOfferProactiveHelp(lastMessage, currentCode);

        if (shouldSuggest) {
            try {
                const suggestion = await this.callGeminiAPI(`
                    Based on the current ${language} code and the user's recent request "${lastMessage}",
                    suggest one brief, helpful improvement or next step. Be concise and actionable.
                    Only suggest if there's a clear, valuable improvement.

                    Code:
                    \`\`\`${language}
                    ${currentCode}
                    \`\`\`

                    Respond with a short suggestion starting with "💡 Suggestion:" or respond with "NO_SUGGESTION" if no clear improvement is needed.
                `);

                if (suggestion && !suggestion.includes('NO_SUGGESTION') && suggestion.trim().length > 0) {
                    this.addMessage('assistant', suggestion);
                }
            } catch (error) {
                // Silently fail for proactive suggestions
                console.log('Proactive suggestion failed:', error);
            }
        }
    }

    shouldOfferProactiveHelp(lastMessage, currentCode) {
        // Only offer help if code exists and message suggests active development
        if (!currentCode || currentCode.length < 50) return false;

        const helpKeywords = ['help', 'improve', 'optimize', 'fix', 'better', 'review', 'check'];
        const codeKeywords = ['function', 'class', 'method', 'variable', 'bug', 'error'];

        const messageWords = lastMessage.toLowerCase().split(' ');
        const hasHelpKeyword = helpKeywords.some(keyword => messageWords.includes(keyword));
        const hasCodeKeyword = codeKeywords.some(keyword => messageWords.includes(keyword));

        return hasHelpKeyword || hasCodeKeyword || lastMessage.length > 20;
    }

    async callGeminiAPI(prompt) {
        if (!this.apiKey) {
            throw new Error('API key not configured');
        }

        const model = document.getElementById('agentic-model').value;
        const temperature = parseFloat(document.getElementById('agentic-temperature').value);

        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${this.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: temperature,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 8192,
                }
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error?.message || 'API request failed');
        }

        const data = await response.json();
        return data.candidates[0]?.content?.parts[0]?.text || 'No response generated';
    }

    async testApiConnection() {
        if (!this.apiKey) {
            this.statusBar.showNotification('Please enter an API key first', 'warning');
            return;
        }

        try {
            const response = await this.callGeminiAPI('Hello! Please respond with "API connection successful" to test the connection.');

            if (response.toLowerCase().includes('successful') || response.toLowerCase().includes('hello')) {
                this.statusBar.showNotification('API connection successful!', 'success');
            } else {
                this.statusBar.showNotification('API connected but unexpected response', 'warning');
            }
        } catch (error) {
            this.statusBar.showNotification(`API connection failed: ${error.message}`, 'error');
        }
    }

    addMessage(type, content, codeContent = null) {
        const messagesContainer = document.getElementById('agentic-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${type}`;

        const timestamp = new Date().toLocaleTimeString();

        let iconName = 'user';
        if (type === 'assistant') iconName = 'ai';
        if (type === 'system') iconName = 'robot';

        messageDiv.innerHTML = `
            <div class="message-header">
                ${createIcon(iconName, 16, 'message-icon')}
                <span class="message-type">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
                <span class="message-time">${timestamp}</span>
            </div>
            <div class="message-content">
                ${this.formatMessage(content)}
                ${codeContent ? `<pre><code>${codeContent}</code></pre>` : ''}
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    formatMessage(content) {
        // Convert markdown-style code blocks to HTML
        return content
            .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>')
            .replace(/`([^`]+)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    extractAndApplyCodeFixes(response, language) {
        const codeBlocks = response.match(/```[\w]*\n([\s\S]*?)```/g);

        if (codeBlocks && codeBlocks.length > 0) {
            const code = codeBlocks[0].replace(/```[\w]*\n/, '').replace(/```$/, '');

            if (this.editorManager.editor) {
                const confirmed = confirm('Apply the suggested code changes?');
                if (confirmed) {
                    this.editorManager.editor.setValue(code);
                    this.statusBar.showNotification('Code changes applied', 'success');
                }
            }
        }
    }

    // Task management methods removed for streamlined chat interface

    clearChat() {
        const messagesContainer = document.getElementById('agentic-messages');
        if (messagesContainer) {
            messagesContainer.innerHTML = `
                <div class="welcome-message">
                    <div class="welcome-icon">
                        ${createIcon('sparkles', 24)}
                    </div>
                    <div class="welcome-content">
                        <h4>Welcome to AI Assistant</h4>
                        <p>I can help you with code generation, review, debugging, refactoring, and documentation. Just ask me naturally!</p>
                        ${!this.isEnabled ? '<p class="config-notice">Configure your API key in settings to get started.</p>' : ''}
                    </div>
                </div>
            `;
        }
    }

    // Task management removed in streamlined interface

    async suggestImprovements(code, language) {
        try {
            const suggestions = await this.callGeminiAPI(`
                Suggest specific improvements for this ${language} code. Be concise and actionable:

                \`\`\`${language}
                ${code}
                \`\`\`
            `);

            this.addMessage('assistant', `Suggestions: ${suggestions}`);
        } catch (error) {
            console.error('Error getting suggestions:', error);
        }
    }

    closePanel() {
        // Hide the panel by adding hidden class and triggering the main app's toggle
        this.container.classList.add('hidden');
        this.container.style.display = 'none';

        // If there's a global toggle function, call it
        if (window.rustCodeApp && window.rustCodeApp.toggleAgenticPanel) {
            // Don't call toggle as it would show the panel again
            // Just ensure it's hidden
        }
    }

    destroy() {
        this.stopContinuousMonitoring();
    }
}

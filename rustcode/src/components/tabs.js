import { createIcon, getFileTypeIcon } from '../icons/icon-library.js';

export class TabManager {
    constructor(container, onTabSelect, onTabClose) {
        this.container = container;
        this.onTabSelect = onTabSelect;
        this.onTabClose = onTabClose;
        this.tabs = new Map();
        this.activeTabId = null;

        this.init();
    }

    init() {
        this.container.innerHTML = '';
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Handle tab overflow scrolling
        this.container.addEventListener('wheel', (e) => {
            if (e.deltaY !== 0) {
                e.preventDefault();
                this.container.scrollLeft += e.deltaY;
            }
        });
    }

    updateTabs(tabs, tabOrder, activeTabId) {
        this.tabs.clear();
        this.container.innerHTML = '';
        this.activeTabId = activeTabId;

        if (!tabs || Object.keys(tabs).length === 0) {
            this.showEmptyState();
            return;
        }

        // Create tabs in the specified order
        tabOrder.forEach(tabId => {
            if (tabs[tabId]) {
                this.tabs.set(tabId, tabs[tabId]);
                this.createTabElement(tabs[tabId]);
            }
        });

        // Update active tab
        if (activeTabId) {
            this.setActiveTab(activeTabId);
        }
    }

    createTabElement(tab) {
        const tabElement = document.createElement('div');
        tabElement.className = 'tab';
        tabElement.dataset.tabId = tab.id;

        // Create tab icon
        const icon = document.createElement('span');
        icon.className = 'tab-icon';
        const fileTypeIcon = getFileTypeIcon(tab.file_name);
        icon.innerHTML = createIcon(fileTypeIcon, 14, `file-icon ${fileTypeIcon}`);
        tabElement.appendChild(icon);

        // Create tab label
        const label = document.createElement('span');
        label.className = 'tab-label';
        label.textContent = tab.file_name;
        label.title = tab.file_path;
        tabElement.appendChild(label);

        // Create modified indicator or close button
        if (tab.is_modified) {
            const modified = document.createElement('span');
            modified.className = 'tab-modified';
            tabElement.appendChild(modified);
            tabElement.classList.add('modified');
        }

        const closeButton = document.createElement('span');
        closeButton.className = 'tab-close icon-button';
        closeButton.innerHTML = createIcon('close', 12, 'tab-close-icon');
        closeButton.title = 'Close';
        tabElement.appendChild(closeButton);

        // Add event listeners
        tabElement.addEventListener('click', (e) => {
            if (e.target === closeButton) {
                e.stopPropagation();
                this.closeTab(tab.id);
            } else {
                this.selectTab(tab.id);
            }
        });

        closeButton.addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeTab(tab.id);
        });

        // Add context menu
        tabElement.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showTabContextMenu(e, tab);
        });

        // Add drag and drop support
        this.setupTabDragAndDrop(tabElement, tab);

        this.container.appendChild(tabElement);
    }

    selectTab(tabId) {
        if (this.activeTabId === tabId) {
            return;
        }

        // Remove active class from previous tab
        if (this.activeTabId) {
            const previousTab = this.container.querySelector(`[data-tab-id="${this.activeTabId}"]`);
            if (previousTab) {
                previousTab.classList.remove('active');
            }
        }

        // Add active class to new tab
        const newTab = this.container.querySelector(`[data-tab-id="${tabId}"]`);
        if (newTab) {
            newTab.classList.add('active');
            this.activeTabId = tabId;

            // Scroll tab into view if needed
            this.scrollTabIntoView(newTab);

            // Notify parent component
            if (this.onTabSelect) {
                this.onTabSelect(tabId);
            }
        }
    }

    setActiveTab(tabId) {
        this.selectTab(tabId);
    }

    closeTab(tabId) {
        const tabElement = this.container.querySelector(`[data-tab-id="${tabId}"]`);
        if (tabElement) {
            // Add closing animation
            tabElement.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
            tabElement.style.opacity = '0';
            tabElement.style.transform = 'scale(0.8)';

            setTimeout(() => {
                if (this.onTabClose) {
                    this.onTabClose(tabId);
                }
            }, 200);
        }
    }

    closeAllTabs() {
        const tabIds = Array.from(this.tabs.keys());
        tabIds.forEach(tabId => this.closeTab(tabId));
    }

    closeOtherTabs(keepTabId) {
        const tabIds = Array.from(this.tabs.keys()).filter(id => id !== keepTabId);
        tabIds.forEach(tabId => this.closeTab(tabId));
    }

    closeTabsToRight(fromTabId) {
        const tabOrder = Array.from(this.container.children).map(el => el.dataset.tabId);
        const fromIndex = tabOrder.indexOf(fromTabId);

        if (fromIndex >= 0) {
            const tabsToClose = tabOrder.slice(fromIndex + 1);
            tabsToClose.forEach(tabId => this.closeTab(tabId));
        }
    }

    scrollTabIntoView(tabElement) {
        const containerRect = this.container.getBoundingClientRect();
        const tabRect = tabElement.getBoundingClientRect();

        if (tabRect.left < containerRect.left) {
            this.container.scrollLeft -= (containerRect.left - tabRect.left);
        } else if (tabRect.right > containerRect.right) {
            this.container.scrollLeft += (tabRect.right - containerRect.right);
        }
    }

    showEmptyState() {
        const emptyState = document.createElement('div');
        emptyState.className = 'tab-bar-empty';
        emptyState.textContent = 'No files open';
        this.container.appendChild(emptyState);
    }

    showTabContextMenu(event, tab) {
        // Remove existing context menu
        const existingMenu = document.querySelector('.tab-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        // Create context menu
        const menu = document.createElement('div');
        menu.className = 'tab-context-menu';
        menu.style.left = event.pageX + 'px';
        menu.style.top = event.pageY + 'px';

        const menuItems = [
            { label: 'Close', action: () => this.closeTab(tab.id), icon: 'close' },
            { label: 'Close Others', action: () => this.closeOtherTabs(tab.id), icon: 'close' },
            { label: 'Close to the Right', action: () => this.closeTabsToRight(tab.id), icon: 'close' },
            { separator: true },
            { label: 'Copy Path', action: () => navigator.clipboard.writeText(tab.file_path), icon: 'copy' },
            { label: 'Reveal in Explorer', action: () => this.revealInExplorer(tab.file_path), icon: 'search' }
        ];

        menuItems.forEach(item => {
            if (item.separator) {
                const separator = document.createElement('div');
                separator.className = 'tab-context-menu-separator';
                menu.appendChild(separator);
            } else {
                const menuItem = document.createElement('div');
                menuItem.className = 'tab-context-menu-item';

                // Add icon if provided
                if (item.icon) {
                    const iconSpan = document.createElement('span');
                    iconSpan.innerHTML = createIcon(item.icon, 14, 'context-menu-icon');
                    menuItem.appendChild(iconSpan);
                }

                const labelSpan = document.createElement('span');
                labelSpan.textContent = item.label;
                menuItem.appendChild(labelSpan);

                menuItem.addEventListener('click', () => {
                    item.action();
                    menu.remove();
                });
                menu.appendChild(menuItem);
            }
        });

        document.body.appendChild(menu);

        // Remove menu when clicking outside
        const removeMenu = (e) => {
            if (!menu.contains(e.target)) {
                menu.remove();
                document.removeEventListener('click', removeMenu);
            }
        };

        setTimeout(() => {
            document.addEventListener('click', removeMenu);
        }, 0);
    }

    setupTabDragAndDrop(tabElement, tab) {
        tabElement.draggable = true;

        tabElement.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', tab.id);
            tabElement.classList.add('dragging');
        });

        tabElement.addEventListener('dragend', () => {
            tabElement.classList.remove('dragging');
        });

        tabElement.addEventListener('dragover', (e) => {
            e.preventDefault();
            const draggingTab = this.container.querySelector('.dragging');
            if (draggingTab && draggingTab !== tabElement) {
                tabElement.classList.add('drag-over');
            }
        });

        tabElement.addEventListener('dragleave', () => {
            tabElement.classList.remove('drag-over');
        });

        tabElement.addEventListener('drop', (e) => {
            e.preventDefault();
            tabElement.classList.remove('drag-over');

            const draggedTabId = e.dataTransfer.getData('text/plain');
            const draggedTab = this.container.querySelector(`[data-tab-id="${draggedTabId}"]`);

            if (draggedTab && draggedTab !== tabElement) {
                // Reorder tabs
                this.reorderTabs(draggedTabId, tab.id);
            }
        });
    }

    reorderTabs(draggedTabId, targetTabId) {
        // TODO: Implement tab reordering
        console.log(`Reorder tab ${draggedTabId} to position of ${targetTabId}`);
    }

    revealInExplorer(filePath) {
        // TODO: Implement reveal in file explorer
        console.log(`Reveal ${filePath} in explorer`);
    }

    getFileExtension(filename) {
        const parts = filename.split('.');
        return parts.length > 1 ? parts.pop().toLowerCase() : '';
    }

    getFileIcon(extension) {
        const iconMap = {
            'rs': '🦀',
            'js': '📜',
            'mjs': '📜',
            'ts': '📘',
            'py': '🐍',
            'html': '🌐',
            'htm': '🌐',
            'css': '🎨',
            'json': '📋',
            'xml': '📄',
            'md': '📝',
            'txt': '📄',
            'yaml': '⚙️',
            'yml': '⚙️',
            'toml': '⚙️',
            'sh': '💻',
            'bash': '💻'
        };

        return iconMap[extension] || '📄';
    }
}

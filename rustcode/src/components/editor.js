import * as monaco from 'monaco-editor';
import { CodeIntelligenceManager } from './code-intelligence.js';
import { registerCustomThemes } from '../themes/monaco-themes.js';

export class EditorManager {
    constructor(container, onContentChange) {
        this.container = container;
        this.onContentChange = onContentChange;
        this.editor = null;
        this.currentModel = null;
        this.isInitialized = false;
        this.codeIntelligence = null;

        this.init();
    }

    async init() {
        try {
            // Register custom themes
            registerCustomThemes();

            // Create Monaco editor instance
            this.editor = monaco.editor.create(this.container, {
                theme: 'rustcode-dark',
                fontSize: 14,
                fontFamily: 'Consolas, Monaco, "Courier New", monospace',
                lineNumbers: 'on',
                minimap: { enabled: true },
                scrollBeyondLastLine: false,
                automaticLayout: true,
                wordWrap: 'on',
                tabSize: 4,
                insertSpaces: true,
                detectIndentation: true,
                trimAutoWhitespace: true,
                renderWhitespace: 'selection',
                renderControlCharacters: false,
                renderIndentGuides: true,
                highlightActiveIndentGuide: true,
                bracketPairColorization: { enabled: true },
                guides: {
                    bracketPairs: true,
                    indentation: true
                },
                suggest: {
                    showKeywords: true,
                    showSnippets: true,
                    showFunctions: true,
                    showConstructors: true,
                    showFields: true,
                    showVariables: true,
                    showClasses: true,
                    showStructs: true,
                    showInterfaces: true,
                    showModules: true,
                    showProperties: true,
                    showEvents: true,
                    showOperators: true,
                    showUnits: true,
                    showValues: true,
                    showConstants: true,
                    showEnums: true,
                    showEnumMembers: true,
                    showColors: true,
                    showFiles: true,
                    showReferences: true,
                    showFolders: true,
                    showTypeParameters: true,
                    showUsers: true,
                    showIssues: true
                },
                quickSuggestions: {
                    other: true,
                    comments: false,
                    strings: false
                },
                parameterHints: { enabled: true },
                hover: { enabled: true },
                contextmenu: true,
                mouseWheelZoom: true,
                multiCursorModifier: 'ctrlCmd',
                accessibilitySupport: 'auto',
                find: {
                    seedSearchStringFromSelection: 'always',
                    autoFindInSelection: 'never'
                }
            });

            // Set up content change listener
            this.editor.onDidChangeModelContent(() => {
                if (this.currentModel && this.onContentChange) {
                    const content = this.editor.getValue();
                    this.onContentChange(content);
                }
            });

            // Set up cursor position change listener
            this.editor.onDidChangeCursorPosition((e) => {
                this.updateCursorPosition(e.position);
            });

            // Set up selection change listener
            this.editor.onDidChangeCursorSelection((e) => {
                this.updateSelection(e.selection);
            });

            // Initialize code intelligence
            this.codeIntelligence = new CodeIntelligenceManager(this);
            this.codeIntelligence.enhanceEditor(this.editor);

            this.isInitialized = true;
            console.log('Monaco editor initialized with code intelligence');
        } catch (error) {
            console.error('Failed to initialize Monaco editor:', error);
            this.showError('Failed to initialize editor');
        }
    }

    openFile(content, language = null) {
        if (!this.isInitialized) {
            console.error('Editor not initialized');
            return;
        }

        try {
            // Dispose of the current model if it exists
            if (this.currentModel) {
                this.currentModel.dispose();
            }

            // Create a new model with the file content
            const uri = monaco.Uri.file(`file-${Date.now()}.${this.getFileExtension(language)}`);
            this.currentModel = monaco.editor.createModel(content, language, uri);

            // Set the model to the editor
            this.editor.setModel(this.currentModel);

            // Focus the editor
            this.editor.focus();

            // Reset cursor position
            this.editor.setPosition({ lineNumber: 1, column: 1 });

            console.log(`Opened file with language: ${language}`);
        } catch (error) {
            console.error('Failed to open file in editor:', error);
            this.showError('Failed to open file');
        }
    }

    getValue() {
        if (!this.editor || !this.currentModel) {
            return '';
        }
        return this.editor.getValue();
    }

    setValue(content) {
        if (!this.editor || !this.currentModel) {
            return;
        }
        this.editor.setValue(content);
    }

    getLanguage() {
        if (!this.currentModel) {
            return null;
        }
        return this.currentModel.getLanguageId();
    }

    setLanguage(language) {
        if (!this.currentModel) {
            return;
        }
        monaco.editor.setModelLanguage(this.currentModel, language);
    }

    showFind() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('actions.find').run();
    }

    showReplace() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('editor.action.startFindReplaceAction').run();
    }

    format() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('editor.action.formatDocument').run();
    }

    undo() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('undo').run();
    }

    redo() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('redo').run();
    }

    cut() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('editor.action.clipboardCutAction').run();
    }

    copy() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('editor.action.clipboardCopyAction').run();
    }

    paste() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('editor.action.clipboardPasteAction').run();
    }

    selectAll() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('editor.action.selectAll').run();
    }

    getCursorPosition() {
        if (!this.editor) {
            return { lineNumber: 1, column: 1 };
        }
        return this.editor.getPosition();
    }

    setCursorPosition(lineNumber, column) {
        if (!this.editor) {
            return;
        }
        this.editor.setPosition({ lineNumber, column });
    }

    getSelection() {
        if (!this.editor) {
            return null;
        }
        return this.editor.getSelection();
    }

    setSelection(startLineNumber, startColumn, endLineNumber, endColumn) {
        if (!this.editor) {
            return;
        }
        this.editor.setSelection({
            startLineNumber,
            startColumn,
            endLineNumber,
            endColumn
        });
    }

    focus() {
        if (!this.editor) {
            return;
        }
        this.editor.focus();
    }

    resize() {
        if (!this.editor) {
            return;
        }
        this.editor.layout();
    }

    dispose() {
        if (this.currentModel) {
            this.currentModel.dispose();
            this.currentModel = null;
        }

        if (this.editor) {
            this.editor.dispose();
            this.editor = null;
        }

        this.isInitialized = false;
    }

    updateCursorPosition(position) {
        // Emit cursor position change event
        const event = new CustomEvent('cursorPositionChange', {
            detail: { line: position.lineNumber, column: position.column }
        });
        document.dispatchEvent(event);
    }

    updateSelection(selection) {
        // Emit selection change event
        const event = new CustomEvent('selectionChange', {
            detail: {
                startLine: selection.startLineNumber,
                startColumn: selection.startColumn,
                endLine: selection.endLineNumber,
                endColumn: selection.endColumn,
                selectedText: this.editor.getModel().getValueInRange(selection)
            }
        });
        document.dispatchEvent(event);
    }

    getFileExtension(language) {
        const extensionMap = {
            'rust': 'rs',
            'javascript': 'js',
            'typescript': 'ts',
            'python': 'py',
            'html': 'html',
            'css': 'css',
            'json': 'json',
            'xml': 'xml',
            'markdown': 'md',
            'yaml': 'yml',
            'toml': 'toml',
            'shell': 'sh',
            'c': 'c',
            'cpp': 'cpp',
            'java': 'java',
            'go': 'go',
            'php': 'php',
            'ruby': 'rb',
            'swift': 'swift',
            'kotlin': 'kt',
            'scala': 'scala',
            'sql': 'sql',
            'dockerfile': 'dockerfile'
        };

        return extensionMap[language] || 'txt';
    }

    showError(message) {
        console.error(message);
        // TODO: Show error in UI
    }
}

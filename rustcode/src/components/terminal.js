import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import { createIcon } from '../icons/icon-library.js';

export class TerminalManager {
    constructor(container) {
        this.container = container;
        this.terminals = new Map();
        this.activeTerminalId = null;
        this.terminalCounter = 0;
        this.tabContainer = null;
        this.terminalContainer = null;

        this.init();
    }

    init() {
        this.createTerminalUI();
        this.createNewTerminal();
    }

    createTerminalUI() {
        this.container.innerHTML = `
            <div class="terminal-header">
                <div class="terminal-tabs" id="terminal-tabs"></div>
                <div class="terminal-actions">
                    <button id="new-terminal-btn" title="New Terminal" class="icon-button">
                        ${createIcon('plus', 16, 'terminal-icon')}
                    </button>
                    <button id="split-terminal-btn" title="Split Terminal" class="icon-button">
                        ${createIcon('split', 16, 'terminal-icon')}
                    </button>
                    <button id="close-terminal-btn" title="Close Terminal" class="icon-button">
                        ${createIcon('close', 16, 'terminal-icon')}
                    </button>
                </div>
            </div>
            <div class="terminal-content" id="terminal-content"></div>
        `;

        this.tabContainer = document.getElementById('terminal-tabs');
        this.terminalContainer = document.getElementById('terminal-content');

        // Set up event listeners
        document.getElementById('new-terminal-btn').addEventListener('click', () => {
            this.createNewTerminal();
        });

        document.getElementById('close-terminal-btn').addEventListener('click', () => {
            this.closeActiveTerminal();
        });
    }

    createNewTerminal(name = null) {
        const terminalId = `terminal-${++this.terminalCounter}`;
        const terminalName = name || `Terminal ${this.terminalCounter}`;

        // Create terminal instance
        const terminal = new Terminal({
            theme: {
                background: '#1e1e1e',
                foreground: '#cccccc',
                cursor: '#ffffff',
                cursorAccent: '#000000',
                selection: '#264f78',
                black: '#000000',
                red: '#cd3131',
                green: '#0dbc79',
                yellow: '#e5e510',
                blue: '#2472c8',
                magenta: '#bc3fbc',
                cyan: '#11a8cd',
                white: '#e5e5e5',
                brightBlack: '#666666',
                brightRed: '#f14c4c',
                brightGreen: '#23d18b',
                brightYellow: '#f5f543',
                brightBlue: '#3b8eea',
                brightMagenta: '#d670d6',
                brightCyan: '#29b8db',
                brightWhite: '#e5e5e5'
            },
            fontSize: 14,
            fontFamily: 'Consolas, Monaco, "Courier New", monospace',
            cursorBlink: true,
            cursorStyle: 'block',
            scrollback: 1000,
            tabStopWidth: 4
        });

        // Add addons
        const fitAddon = new FitAddon();
        const webLinksAddon = new WebLinksAddon();

        terminal.loadAddon(fitAddon);
        terminal.loadAddon(webLinksAddon);

        // Create terminal container
        const terminalDiv = document.createElement('div');
        terminalDiv.className = 'terminal-instance';
        terminalDiv.id = terminalId;
        terminalDiv.style.display = 'none';
        this.terminalContainer.appendChild(terminalDiv);

        // Open terminal
        terminal.open(terminalDiv);
        fitAddon.fit();

        // Create web-based shell simulation
        const shell = new WebShell(terminal);

        // Store terminal data
        this.terminals.set(terminalId, {
            terminal,
            fitAddon,
            shell,
            name: terminalName,
            element: terminalDiv
        });

        // Create tab
        this.createTerminalTab(terminalId, terminalName);

        // Set as active
        this.setActiveTerminal(terminalId);

        // Handle resize
        window.addEventListener('resize', () => {
            if (this.activeTerminalId === terminalId) {
                fitAddon.fit();
            }
        });

        return terminalId;
    }

    createTerminalTab(terminalId, name) {
        const tab = document.createElement('div');
        tab.className = 'terminal-tab';
        tab.dataset.terminalId = terminalId;
        tab.innerHTML = `
            <span class="terminal-tab-name">${name}</span>
            <button class="terminal-tab-close icon-button" title="Close">
                ${createIcon('close', 12, 'tab-close-icon')}
            </button>
        `;

        tab.addEventListener('click', (e) => {
            if (!e.target.classList.contains('terminal-tab-close')) {
                this.setActiveTerminal(terminalId);
            }
        });

        tab.querySelector('.terminal-tab-close').addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeTerminal(terminalId);
        });

        this.tabContainer.appendChild(tab);
    }

    setActiveTerminal(terminalId) {
        // Hide all terminals
        this.terminals.forEach((termData, id) => {
            termData.element.style.display = 'none';
            const tab = this.tabContainer.querySelector(`[data-terminal-id="${id}"]`);
            if (tab) tab.classList.remove('active');
        });

        // Show active terminal
        const termData = this.terminals.get(terminalId);
        if (termData) {
            termData.element.style.display = 'block';
            termData.fitAddon.fit();
            termData.terminal.focus();

            const tab = this.tabContainer.querySelector(`[data-terminal-id="${terminalId}"]`);
            if (tab) tab.classList.add('active');

            this.activeTerminalId = terminalId;
        }
    }

    closeTerminal(terminalId) {
        const termData = this.terminals.get(terminalId);
        if (!termData) return;

        // Dispose terminal
        termData.terminal.dispose();
        termData.element.remove();

        // Remove tab
        const tab = this.tabContainer.querySelector(`[data-terminal-id="${terminalId}"]`);
        if (tab) tab.remove();

        // Remove from map
        this.terminals.delete(terminalId);

        // Set new active terminal
        if (this.activeTerminalId === terminalId) {
            const remainingTerminals = Array.from(this.terminals.keys());
            if (remainingTerminals.length > 0) {
                this.setActiveTerminal(remainingTerminals[0]);
            } else {
                this.activeTerminalId = null;
                this.createNewTerminal(); // Always have at least one terminal
            }
        }
    }

    closeActiveTerminal() {
        if (this.activeTerminalId) {
            this.closeTerminal(this.activeTerminalId);
        }
    }

    resize() {
        if (this.activeTerminalId) {
            const termData = this.terminals.get(this.activeTerminalId);
            if (termData) {
                termData.fitAddon.fit();
            }
        }
    }

    clear() {
        if (this.activeTerminalId) {
            const termData = this.terminals.get(this.activeTerminalId);
            if (termData) {
                termData.terminal.clear();
            }
        }
    }

    executeCommand(command) {
        if (this.activeTerminalId) {
            const termData = this.terminals.get(this.activeTerminalId);
            if (termData && termData.shell) {
                termData.shell.executeCommand(command);
            }
        }
    }
}

// Web-based shell simulation
class WebShell {
    constructor(terminal) {
        this.terminal = terminal;
        this.currentPath = '/';
        this.commandHistory = [];
        this.historyIndex = -1;
        this.currentLine = '';

        this.setupTerminal();
        this.showPrompt();
    }

    setupTerminal() {
        this.terminal.onData((data) => {
            this.handleInput(data);
        });
    }

    handleInput(data) {
        const char = data.charCodeAt(0);

        if (char === 13) { // Enter
            this.terminal.write('\r\n');
            this.executeCommand(this.currentLine);
            this.currentLine = '';
            this.showPrompt();
        } else if (char === 127) { // Backspace
            if (this.currentLine.length > 0) {
                this.currentLine = this.currentLine.slice(0, -1);
                this.terminal.write('\b \b');
            }
        } else if (char === 27) { // Escape sequences (arrow keys)
            // Handle arrow keys for command history
            return;
        } else if (char >= 32) { // Printable characters
            this.currentLine += data;
            this.terminal.write(data);
        }
    }

    executeCommand(command) {
        command = command.trim();
        if (!command) return;

        this.commandHistory.push(command);
        this.historyIndex = this.commandHistory.length;

        const parts = command.split(' ');
        const cmd = parts[0];
        const args = parts.slice(1);

        switch (cmd) {
            case 'help':
                this.showHelp();
                break;
            case 'clear':
                this.terminal.clear();
                break;
            case 'ls':
            case 'dir':
                this.listFiles();
                break;
            case 'pwd':
                this.terminal.write(`${this.currentPath}\r\n`);
                break;
            case 'echo':
                this.terminal.write(`${args.join(' ')}\r\n`);
                break;
            case 'date':
                this.terminal.write(`${new Date().toString()}\r\n`);
                break;
            case 'whoami':
                this.terminal.write('rustcode-user\r\n');
                break;
            default:
                this.terminal.write(`Command not found: ${cmd}\r\n`);
                this.terminal.write('Type "help" for available commands.\r\n');
        }
    }

    showPrompt() {
        this.terminal.write(`\x1b[32mrustcode\x1b[0m:\x1b[34m${this.currentPath}\x1b[0m$ `);
    }

    showHelp() {
        const helpText = `
Available commands:
  help     - Show this help message
  clear    - Clear the terminal
  ls, dir  - List files in current directory
  pwd      - Show current directory
  echo     - Echo text
  date     - Show current date and time
  whoami   - Show current user
`;
        this.terminal.write(helpText);
    }

    async listFiles() {
        try {
            if (window.rustCodeApp && window.rustCodeApp.invoke) {
                const tree = await window.rustCodeApp.invoke('get_directory_tree', { path: this.currentPath });
                if (tree && tree.children) {
                    tree.children.forEach(item => {
                        const type = item.is_directory ? 'd' : '-';
                        const name = item.is_directory ? `\x1b[34m${item.name}\x1b[0m` : item.name;
                        this.terminal.write(`${type}  ${name}\r\n`);
                    });
                } else {
                    this.terminal.write('No files found.\r\n');
                }
            } else {
                this.terminal.write('File system not available.\r\n');
            }
        } catch (error) {
            this.terminal.write(`Error: ${error.message}\r\n`);
        }
    }
}

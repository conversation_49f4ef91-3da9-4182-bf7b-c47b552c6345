import * as monaco from 'monaco-editor';

export function registerCustomThemes() {
    // RustCode Dark Theme
    monaco.editor.defineTheme('rustcode-dark', {
        base: 'vs-dark',
        inherit: true,
        rules: [
            { token: 'comment', foreground: '6A9955', fontStyle: 'italic' },
            { token: 'keyword', foreground: '569CD6' },
            { token: 'operator', foreground: 'D4D4D4' },
            { token: 'namespace', foreground: '4EC9B0' },
            { token: 'type', foreground: '4EC9B0' },
            { token: 'struct', foreground: '4EC9B0' },
            { token: 'class', foreground: '4EC9B0' },
            { token: 'interface', foreground: '4EC9B0' },
            { token: 'enum', foreground: '4EC9B0' },
            { token: 'function', foreground: 'DCDCAA' },
            { token: 'method', foreground: 'DCDCAA' },
            { token: 'variable', foreground: '9CDCFE' },
            { token: 'parameter', foreground: '9CDCFE' },
            { token: 'property', foreground: '9CDCFE' },
            { token: 'string', foreground: 'CE9178' },
            { token: 'string.escape', foreground: 'D7BA7D' },
            { token: 'number', foreground: 'B5CEA8' },
            { token: 'regexp', foreground: 'D16969' },
            { token: 'delimiter', foreground: 'D4D4D4' },
            { token: 'tag', foreground: '569CD6' },
            { token: 'attribute.name', foreground: '92C5F8' },
            { token: 'attribute.value', foreground: 'CE9178' },
            { token: 'macro', foreground: 'C586C0' },
            { token: 'annotation', foreground: 'C586C0' },
            { token: 'decorator', foreground: 'C586C0' }
        ],
        colors: {
            'editor.background': '#1e1e1e',
            'editor.foreground': '#d4d4d4',
            'editorLineNumber.foreground': '#858585',
            'editorLineNumber.activeForeground': '#c6c6c6',
            'editor.selectionBackground': '#264f78',
            'editor.selectionHighlightBackground': '#add6ff26',
            'editor.inactiveSelectionBackground': '#3a3d41',
            'editorIndentGuide.background': '#404040',
            'editorIndentGuide.activeBackground': '#707070',
            'editor.wordHighlightBackground': '#575757b8',
            'editor.wordHighlightStrongBackground': '#004972b8',
            'editorCursor.foreground': '#aeafad',
            'editor.lineHighlightBackground': '#2a2d2e',
            'editorWhitespace.foreground': '#404040',
            'editorBracketMatch.background': '#0064001a',
            'editorBracketMatch.border': '#888888',
            'editorError.foreground': '#f48771',
            'editorWarning.foreground': '#ffcc02',
            'editorInfo.foreground': '#75beff',
            'editorHint.foreground': '#eeeeeeb3',
            'editorGutter.background': '#1e1e1e',
            'editorGutter.modifiedBackground': '#1b81a8',
            'editorGutter.addedBackground': '#487e02',
            'editorGutter.deletedBackground': '#f85149',
            'minimapGutter.modifiedBackground': '#1b81a8',
            'minimapGutter.addedBackground': '#487e02',
            'minimapGutter.deletedBackground': '#f85149',
            'editorOverviewRuler.border': '#7f7f7f4d',
            'editorOverviewRuler.selectionHighlightForeground': '#a0a0a0cc',
            'editorOverviewRuler.wordHighlightForeground': '#a0a0a0cc',
            'editorOverviewRuler.wordHighlightStrongForeground': '#c0a0c0cc',
            'editorOverviewRuler.modifiedForeground': '#1b81a8',
            'editorOverviewRuler.addedForeground': '#487e02',
            'editorOverviewRuler.deletedForeground': '#f85149',
            'editorOverviewRuler.errorForeground': '#f85149',
            'editorOverviewRuler.warningForeground': '#ffcc02',
            'editorOverviewRuler.infoForeground': '#75beff'
        }
    });

    // RustCode Light Theme
    monaco.editor.defineTheme('rustcode-light', {
        base: 'vs',
        inherit: true,
        rules: [
            { token: 'comment', foreground: '008000', fontStyle: 'italic' },
            { token: 'keyword', foreground: '0000FF' },
            { token: 'operator', foreground: '000000' },
            { token: 'namespace', foreground: '267F99' },
            { token: 'type', foreground: '267F99' },
            { token: 'struct', foreground: '267F99' },
            { token: 'class', foreground: '267F99' },
            { token: 'interface', foreground: '267F99' },
            { token: 'enum', foreground: '267F99' },
            { token: 'function', foreground: '795E26' },
            { token: 'method', foreground: '795E26' },
            { token: 'variable', foreground: '001080' },
            { token: 'parameter', foreground: '001080' },
            { token: 'property', foreground: '001080' },
            { token: 'string', foreground: 'A31515' },
            { token: 'string.escape', foreground: 'FF0000' },
            { token: 'number', foreground: '098658' },
            { token: 'regexp', foreground: '811F3F' },
            { token: 'delimiter', foreground: '000000' },
            { token: 'tag', foreground: '800000' },
            { token: 'attribute.name', foreground: 'FF0000' },
            { token: 'attribute.value', foreground: '0451A5' },
            { token: 'macro', foreground: 'AF00DB' },
            { token: 'annotation', foreground: 'AF00DB' },
            { token: 'decorator', foreground: 'AF00DB' }
        ],
        colors: {
            'editor.background': '#ffffff',
            'editor.foreground': '#000000',
            'editorLineNumber.foreground': '#237893',
            'editorLineNumber.activeForeground': '#0B216F',
            'editor.selectionBackground': '#ADD6FF',
            'editor.selectionHighlightBackground': '#ADD6FF4D',
            'editor.inactiveSelectionBackground': '#E5EBF1',
            'editorIndentGuide.background': '#D3D3D3',
            'editorIndentGuide.activeBackground': '#939393',
            'editor.wordHighlightBackground': '#57575740',
            'editor.wordHighlightStrongBackground': '#0073E64D',
            'editorCursor.foreground': '#000000',
            'editor.lineHighlightBackground': '#F0F0F0',
            'editorWhitespace.foreground': '#BFBFBF',
            'editorBracketMatch.background': '#0064001A',
            'editorBracketMatch.border': '#B9B9B9',
            'editorError.foreground': '#E51400',
            'editorWarning.foreground': '#BF8803',
            'editorInfo.foreground': '#1a85ff',
            'editorHint.foreground': '#6c6c6c',
            'editorGutter.background': '#ffffff',
            'editorGutter.modifiedBackground': '#1B81A8',
            'editorGutter.addedBackground': '#2EA043',
            'editorGutter.deletedBackground': '#F85149'
        }
    });

    // RustCode High Contrast Theme
    monaco.editor.defineTheme('rustcode-high-contrast', {
        base: 'hc-black',
        inherit: true,
        rules: [
            { token: 'comment', foreground: '7CA668', fontStyle: 'italic' },
            { token: 'keyword', foreground: '569CD6' },
            { token: 'operator', foreground: 'FFFFFF' },
            { token: 'namespace', foreground: '4EC9B0' },
            { token: 'type', foreground: '4EC9B0' },
            { token: 'struct', foreground: '4EC9B0' },
            { token: 'class', foreground: '4EC9B0' },
            { token: 'interface', foreground: '4EC9B0' },
            { token: 'enum', foreground: '4EC9B0' },
            { token: 'function', foreground: 'DCDCAA' },
            { token: 'method', foreground: 'DCDCAA' },
            { token: 'variable', foreground: '9CDCFE' },
            { token: 'parameter', foreground: '9CDCFE' },
            { token: 'property', foreground: '9CDCFE' },
            { token: 'string', foreground: 'CE9178' },
            { token: 'string.escape', foreground: 'D7BA7D' },
            { token: 'number', foreground: 'B5CEA8' },
            { token: 'regexp', foreground: 'D16969' },
            { token: 'delimiter', foreground: 'FFFFFF' },
            { token: 'tag', foreground: '569CD6' },
            { token: 'attribute.name', foreground: '92C5F8' },
            { token: 'attribute.value', foreground: 'CE9178' },
            { token: 'macro', foreground: 'C586C0' },
            { token: 'annotation', foreground: 'C586C0' },
            { token: 'decorator', foreground: 'C586C0' }
        ],
        colors: {
            'editor.background': '#000000',
            'editor.foreground': '#FFFFFF',
            'editorLineNumber.foreground': '#FFFFFF',
            'editorLineNumber.activeForeground': '#FFFFFF',
            'editor.selectionBackground': '#FFFFFF40',
            'editor.selectionHighlightBackground': '#FFFFFF20',
            'editor.inactiveSelectionBackground': '#FFFFFF20',
            'editorIndentGuide.background': '#FFFFFF40',
            'editorIndentGuide.activeBackground': '#FFFFFF80',
            'editor.wordHighlightBackground': '#FFFFFF40',
            'editor.wordHighlightStrongBackground': '#FFFFFF60',
            'editorCursor.foreground': '#FFFFFF',
            'editor.lineHighlightBackground': '#FFFFFF0A',
            'editorWhitespace.foreground': '#FFFFFF40',
            'editorBracketMatch.background': '#FFFFFF20',
            'editorBracketMatch.border': '#FFFFFF',
            'editorError.foreground': '#FF0000',
            'editorWarning.foreground': '#FFFF00',
            'editorInfo.foreground': '#00FFFF',
            'editorHint.foreground': '#FFFFFF80',
            'editorGutter.background': '#000000',
            'editorGutter.modifiedBackground': '#0080FF',
            'editorGutter.addedBackground': '#00FF00',
            'editorGutter.deletedBackground': '#FF0000'
        }
    });

    console.log('Custom Monaco themes registered');
}

export function getThemeForSettings(settingsTheme) {
    switch (settingsTheme) {
        case 'light':
            return 'rustcode-light';
        case 'high-contrast':
            return 'rustcode-high-contrast';
        default:
            return 'rustcode-dark';
    }
}

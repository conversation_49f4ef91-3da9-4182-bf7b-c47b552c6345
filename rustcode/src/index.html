<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RustCode</title>
    <link rel="stylesheet" href="./styles/main.css">
    <link rel="stylesheet" href="./styles/file-explorer.css">
    <link rel="stylesheet" href="./styles/editor.css">
    <link rel="stylesheet" href="./styles/tabs.css">
    <link rel="stylesheet" href="./styles/status-bar.css">
    <link rel="stylesheet" href="./styles/command-palette.css">
    <link rel="stylesheet" href="./styles/terminal.css">
    <link rel="stylesheet" href="./styles/search.css">
    <link rel="stylesheet" href="./styles/settings.css">
    <link rel="stylesheet" href="./styles/icons.css">
    <link rel="stylesheet" href="./styles/agentic-coding.css">
</head>
<body>
    <div id="app">
        <!-- Title Bar -->
        <div id="title-bar" data-tauri-drag-region>
            <div class="title-bar-content">
                <div class="title-bar-title">RustCode</div>
                <div class="title-bar-controls">
                    <button id="minimize-btn" class="title-bar-button">−</button>
                    <button id="maximize-btn" class="title-bar-button">□</button>
                    <button id="close-btn" class="title-bar-button">×</button>
                </div>
            </div>
        </div>

        <!-- Menu Bar -->
        <div id="menu-bar">
            <div class="menu-item" data-menu="file">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2 2C1.44772 2 1 2.44772 1 3V13C1 13.5523 1.44772 14 2 14H14C14.5523 14 15 13.5523 15 13V5.5L11.5 2H2Z" stroke="currentColor" stroke-width="1" fill="none"/>
                        <path d="M11 2V5H14" stroke="currentColor" stroke-width="1" fill="none"/>
                    </svg>
                </span>
                File
            </div>
            <div class="menu-item" data-menu="edit">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M11.5 2.5L13.5 4.5L5 13H3V11L11.5 2.5Z" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"/>
                        <path d="M10.5 3.5L12.5 5.5" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"/>
                    </svg>
                </span>
                Edit
            </div>
            <div class="menu-item" data-menu="view">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 8C1 8 3 3 8 3C13 3 15 8 15 8C15 8 13 13 8 13C3 13 1 8 1 8Z" stroke="currentColor" stroke-width="1.5"/>
                        <circle cx="8" cy="8" r="2" stroke="currentColor" stroke-width="1.5"/>
                    </svg>
                </span>
                View
            </div>
            <div class="menu-item" data-menu="search">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="7" cy="7" r="4" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M11 11L14 14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    </svg>
                </span>
                Search
            </div>
            <div class="menu-item" data-menu="terminal">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="1" y="3" width="14" height="10" rx="1" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M4 7L6 9L4 11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M8 11H12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    </svg>
                </span>
                Terminal
            </div>
            <div class="menu-item" data-menu="settings">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="8" cy="8" r="2" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M8 1V3M8 13V15M15 8H13M3 8H1M12.5 3.5L11.1 4.9M4.9 11.1L3.5 12.5M12.5 12.5L11.1 11.1M4.9 4.9L3.5 3.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    </svg>
                </span>
                Settings
            </div>
            <div class="menu-item" data-menu="agentic">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M5 7L7 9L11 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <circle cx="8" cy="8" r="2" fill="currentColor" opacity="0.3"/>
                    </svg>
                </span>
                Agentic
            </div>
            <div class="menu-item" data-menu="help">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M6 6C6 4.89543 6.89543 4 8 4C9.10457 4 10 4.89543 10 6C10 7 9 7.5 8 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                        <circle cx="8" cy="11" r="0.5" fill="currentColor"/>
                    </svg>
                </span>
                Help
            </div>
        </div>

        <!-- Main Content -->
        <div id="main-content">
            <!-- Sidebar -->
            <div id="sidebar" class="panel">
                <div class="sidebar-header">
                    <div class="sidebar-title">Explorer</div>
                    <div class="sidebar-actions">
                        <button id="new-file-btn" title="New File" class="icon-button">
                            <!-- Icon will be populated by JavaScript -->
                        </button>
                        <button id="new-folder-btn" title="New Folder" class="icon-button">
                            <!-- Icon will be populated by JavaScript -->
                        </button>
                        <button id="refresh-btn" title="Refresh" class="icon-button">
                            <!-- Icon will be populated by JavaScript -->
                        </button>
                    </div>
                </div>
                <div id="file-explorer"></div>
            </div>

            <!-- Search Panel -->
            <div id="search-panel" class="panel"></div>

            <!-- Search Resize Handle -->
            <div id="search-resize-handle" class="search-resize-handle" style="display: none;"></div>

            <!-- Resize Handle -->
            <div id="resize-handle"></div>

            <!-- Editor Area -->
            <div id="editor-area" class="panel">
                <!-- Tab Bar -->
                <div id="tab-bar"></div>

                <!-- Editor Container -->
                <div id="editor-container">
                    <div id="welcome-screen">
                        <div class="welcome-content">
                            <h1>Welcome to RustCode</h1>
                            <p>A minimalistic online code editor</p>
                            <div class="welcome-features">
                                <div class="feature">
                                    <span>Create and edit files with syntax highlighting</span>
                                </div>
                                <div class="feature">
                                    <span>Auto-save to browser storage</span>
                                </div>
                                <div class="feature">
                                    <span>Familiar keyboard shortcuts</span>
                                </div>
                            </div>
                            <div class="welcome-actions">
                                <button id="open-folder-btn" class="welcome-button">Start Workspace</button>
                                <button id="new-file-welcome-btn" class="welcome-button">New File</button>
                            </div>
                            <div class="welcome-shortcuts">
                                <p>Quick shortcuts: <kbd>Ctrl+N</kbd> New File • <kbd>Ctrl+P</kbd> Quick Open • <kbd>Ctrl+Shift+P</kbd> Commands • <kbd>Ctrl+`</kbd> Terminal</p>
                            </div>
                        </div>
                    </div>
                    <div id="monaco-editor" style="display: none;"></div>
                </div>

                <!-- Terminal Resize Handle -->
                <div id="terminal-resize-handle" class="terminal-resize-handle" style="display: none;"></div>

                <!-- Terminal Panel -->
                <div id="terminal-panel" style="display: none;"></div>
            </div>
        </div>

        <!-- Status Bar -->
        <div id="status-bar">
            <div class="status-left">
                <span id="status-file-info"></span>
                <span id="status-cursor-position"></span>
            </div>
            <div class="status-right">
                <span id="status-language"></span>
                <span id="status-encoding">UTF-8</span>
                <span id="status-line-ending">LF</span>
            </div>
        </div>

        <!-- Command Palette -->
        <div id="command-palette" class="hidden">
            <div class="command-palette-content">
                <input type="text" id="command-input" placeholder="Type a command...">
                <div id="command-results"></div>
            </div>
        </div>

        <!-- Context Menu -->
        <div id="context-menu" class="hidden">
            <div class="context-menu-item" data-action="open">Open</div>
            <div class="context-menu-item" data-action="rename">Rename</div>
            <div class="context-menu-item" data-action="delete">Delete</div>
            <div class="context-menu-separator"></div>
            <div class="context-menu-item" data-action="new-file">New File</div>
            <div class="context-menu-item" data-action="new-folder">New Folder</div>
        </div>

        <!-- Search Panel -->
        <div id="search-panel" class="hidden">
            <div class="search-header">
                <input type="text" id="search-input" placeholder="Search in files...">
                <button id="search-btn">Search</button>
                <button id="close-search-btn">×</button>
            </div>
            <div id="search-results"></div>
        </div>

        <!-- Agentic Coding Panel -->
        <div id="agentic-panel" class="hidden">
            <!-- Agentic coding content will be populated by JavaScript -->
        </div>

        <!-- Settings Panel -->
        <div id="settings-panel">
            <div class="settings-modal"></div>
        </div>
    </div>

    <script type="module" src="./main.js"></script>
</body>
</html>

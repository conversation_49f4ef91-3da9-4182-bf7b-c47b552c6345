<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RustCode</title>
    <link rel="stylesheet" href="./styles/main.css">
    <link rel="stylesheet" href="./styles/file-explorer.css">
    <link rel="stylesheet" href="./styles/editor.css">
    <link rel="stylesheet" href="./styles/tabs.css">
    <link rel="stylesheet" href="./styles/status-bar.css">
    <link rel="stylesheet" href="./styles/command-palette.css">
    <link rel="stylesheet" href="./styles/terminal.css">
    <link rel="stylesheet" href="./styles/search.css">
    <link rel="stylesheet" href="./styles/settings.css">
    <link rel="stylesheet" href="./styles/icons.css">
    <link rel="stylesheet" href="./styles/agentic-coding.css">
</head>
<body>
    <div id="app">
        <!-- Title Bar -->
        <div id="title-bar" data-tauri-drag-region>
            <div class="title-bar-content">
                <div class="title-bar-title">RustCode</div>
                <div class="title-bar-controls">
                    <button id="minimize-btn" class="title-bar-button">−</button>
                    <button id="maximize-btn" class="title-bar-button">□</button>
                    <button id="close-btn" class="title-bar-button">×</button>
                </div>
            </div>
        </div>

        <!-- Menu Bar -->
        <div id="menu-bar">
            <div class="menu-item" data-menu="file">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2 2C1.44772 2 1 2.44772 1 3V13C1 13.5523 1.44772 14 2 14H14C14.5523 14 15 13.5523 15 13V5.5L11.5 2H2Z" stroke="currentColor" stroke-width="1" fill="none"/>
                        <path d="M11 2V5H14" stroke="currentColor" stroke-width="1" fill="none"/>
                    </svg>
                </span>
                File
            </div>
            <div class="menu-item" data-menu="edit">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M11.5 2.5L13.5 4.5L5 13H3V11L11.5 2.5Z" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"/>
                        <path d="M10.5 3.5L12.5 5.5" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"/>
                    </svg>
                </span>
                Edit
            </div>
            <div class="menu-item" data-menu="view">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 8C1 8 3 3 8 3C13 3 15 8 15 8C15 8 13 13 8 13C3 13 1 8 1 8Z" stroke="currentColor" stroke-width="1.5"/>
                        <circle cx="8" cy="8" r="2" stroke="currentColor" stroke-width="1.5"/>
                    </svg>
                </span>
                View
            </div>
            <div class="menu-item" data-menu="search">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="7" cy="7" r="4" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M11 11L14 14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    </svg>
                </span>
                Search
            </div>
            <div class="menu-item" data-menu="terminal">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="1" y="3" width="14" height="10" rx="1" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M4 7L6 9L4 11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M8 11H12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    </svg>
                </span>
                Terminal
            </div>
            <div class="menu-item" data-menu="settings">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8 10C9.10457 10 10 9.10457 10 8C10 6.89543 9.10457 6 8 6C6.89543 6 6 6.89543 6 8C6 9.10457 6.89543 10 8 10Z" stroke="currentColor" stroke-width="1.2" fill="none"/>
                        <path d="M6.7 1.5L7.3 1.5C7.6866 1.5 8 1.8134 8 2.2L8 2.8C8.3 2.9 8.6 3.1 8.8 3.3L9.3 2.8C9.5828 2.5172 10.0172 2.5172 10.3 2.8L10.7 3.2C10.9828 3.4828 10.9828 3.9172 10.7 4.2L10.2 4.7C10.4 4.9 10.6 5.2 10.7 5.5L11.3 5.5C11.6866 5.5 12 5.8134 12 6.2L12 6.8C12 7.1866 11.6866 7.5 11.3 7.5L10.7 7.5C10.6 7.8 10.4 8.1 10.2 8.3L10.7 8.8C10.9828 9.0828 10.9828 9.5172 10.7 9.8L10.3 10.2C10.0172 10.4828 9.5828 10.4828 9.3 10.2L8.8 9.7C8.6 9.9 8.3 10.1 8 10.2L8 10.8C8 11.1866 7.6866 11.5 7.3 11.5L6.7 11.5C6.3134 11.5 6 11.1866 6 10.8L6 10.2C5.7 10.1 5.4 9.9 5.2 9.7L4.7 10.2C4.4172 10.4828 3.9828 10.4828 3.7 10.2L3.3 9.8C3.0172 9.5172 3.0172 9.0828 3.3 8.8L3.8 8.3C3.6 8.1 3.4 7.8 3.3 7.5L2.7 7.5C2.3134 7.5 2 7.1866 2 6.8L2 6.2C2 5.8134 2.3134 5.5 2.7 5.5L3.3 5.5C3.4 5.2 3.6 4.9 3.8 4.7L3.3 4.2C3.0172 3.9172 3.0172 3.4828 3.3 3.2L3.7 2.8C3.9828 2.5172 4.4172 2.5172 4.7 2.8L5.2 3.3C5.4 3.1 5.7 2.9 6 2.8L6 2.2C6 1.8134 6.3134 1.5 6.7 1.5Z" stroke="currentColor" stroke-width="1.2" fill="none"/>
                    </svg>
                </span>
                Settings
            </div>
            <div class="menu-item" data-menu="agentic">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M8 6C9.10457 6 10 6.89543 10 8C10 9.10457 9.10457 10 8 10C6.89543 10 6 9.10457 6 8C6 6.89543 6.89543 6 8 6Z" fill="currentColor" opacity="0.2"/>
                        <circle cx="6.5" cy="7.5" r="1" fill="currentColor"/>
                        <circle cx="9.5" cy="7.5" r="1" fill="currentColor"/>
                        <path d="M6.5 9.5C7.5 10.5 8.5 10.5 9.5 9.5" stroke="currentColor" stroke-width="1.2" stroke-linecap="round"/>
                    </svg>
                </span>
                Agentic
            </div>
            <div class="menu-item" data-menu="help">
                <span class="menu-icon">
                    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M6 6C6 4.89543 6.89543 4 8 4C9.10457 4 10 4.89543 10 6C10 7 9 7.5 8 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                        <circle cx="8" cy="11" r="0.5" fill="currentColor"/>
                    </svg>
                </span>
                Help
            </div>
        </div>

        <!-- Main Content -->
        <div id="main-content">
            <!-- Sidebar -->
            <div id="sidebar" class="panel">
                <div class="sidebar-header">
                    <div class="sidebar-title">Explorer</div>
                    <div class="sidebar-actions">
                        <button id="new-file-btn" title="New File" class="icon-button">
                            <!-- Icon will be populated by JavaScript -->
                        </button>
                        <button id="new-folder-btn" title="New Folder" class="icon-button">
                            <!-- Icon will be populated by JavaScript -->
                        </button>
                        <button id="refresh-btn" title="Refresh" class="icon-button">
                            <!-- Icon will be populated by JavaScript -->
                        </button>
                    </div>
                </div>
                <div id="file-explorer"></div>
            </div>

            <!-- Search Panel -->
            <div id="search-panel" class="panel"></div>

            <!-- Search Resize Handle -->
            <div id="search-resize-handle" class="search-resize-handle" style="display: none;"></div>

            <!-- Resize Handle -->
            <div id="resize-handle"></div>

            <!-- Editor Area -->
            <div id="editor-area" class="panel">
                <!-- Tab Bar -->
                <div id="tab-bar"></div>

                <!-- Editor Container -->
                <div id="editor-container">
                    <div id="welcome-screen">
                        <div class="welcome-content">
                            <h1>Welcome to RustCode</h1>
                            <p>A minimalistic online code editor</p>
                            <div class="welcome-features">
                                <div class="feature">
                                    <span>Create and edit files with syntax highlighting</span>
                                </div>
                                <div class="feature">
                                    <span>Auto-save to browser storage</span>
                                </div>
                                <div class="feature">
                                    <span>Familiar keyboard shortcuts</span>
                                </div>
                            </div>
                            <div class="welcome-actions">
                                <button id="open-folder-btn" class="welcome-button">Start Workspace</button>
                                <button id="new-file-welcome-btn" class="welcome-button">New File</button>
                            </div>
                            <div class="welcome-shortcuts">
                                <p>Quick shortcuts: <kbd>Ctrl+N</kbd> New File • <kbd>Ctrl+P</kbd> Quick Open • <kbd>Ctrl+Shift+P</kbd> Commands • <kbd>Ctrl+`</kbd> Terminal</p>
                            </div>
                        </div>
                    </div>
                    <div id="monaco-editor" style="display: none;"></div>
                </div>

                <!-- Terminal Resize Handle -->
                <div id="terminal-resize-handle" class="terminal-resize-handle" style="display: none;"></div>

                <!-- Terminal Panel -->
                <div id="terminal-panel" style="display: none;"></div>
            </div>
        </div>

        <!-- Status Bar -->
        <div id="status-bar">
            <div class="status-left">
                <span id="status-file-info"></span>
                <span id="status-cursor-position"></span>
            </div>
            <div class="status-right">
                <span id="status-language"></span>
                <span id="status-encoding">UTF-8</span>
                <span id="status-line-ending">LF</span>
            </div>
        </div>

        <!-- Command Palette -->
        <div id="command-palette" class="hidden">
            <div class="command-palette-content">
                <input type="text" id="command-input" placeholder="Type a command...">
                <div id="command-results"></div>
            </div>
        </div>

        <!-- Context Menu -->
        <div id="context-menu" class="hidden">
            <div class="context-menu-item" data-action="open">Open</div>
            <div class="context-menu-item" data-action="rename">Rename</div>
            <div class="context-menu-item" data-action="delete">Delete</div>
            <div class="context-menu-separator"></div>
            <div class="context-menu-item" data-action="new-file">New File</div>
            <div class="context-menu-item" data-action="new-folder">New Folder</div>
        </div>

        <!-- Search Panel -->
        <div id="search-panel" class="hidden">
            <div class="search-header">
                <input type="text" id="search-input" placeholder="Search in files...">
                <button id="search-btn">Search</button>
                <button id="close-search-btn">×</button>
            </div>
            <div id="search-results"></div>
        </div>

        <!-- Agentic Coding Panel -->
        <div id="agentic-panel" class="hidden">
            <!-- Agentic coding content will be populated by JavaScript -->
        </div>

        <!-- Settings Panel -->
        <div id="settings-panel">
            <div class="settings-modal"></div>
        </div>
    </div>

    <script type="module" src="./main.js"></script>
</body>
</html>

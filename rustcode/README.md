# RustCode

A professional-grade online code editor built with modern web technologies. RustCode provides a complete development environment in your browser with advanced features for coding, file management, and productivity.

## 🚀 Features

### 🗂️ Advanced File Management System
- **Drag & Drop Operations**: Move files and folders with intuitive drag and drop
- **Context Menus**: Right-click for comprehensive file operations (rename, delete, copy, paste)
- **Inline Renaming**: Double-click with Ctrl/Cmd to rename files and folders
- **File Operations**: Cut, copy, paste, and organize your workspace efficiently
- **Clean File Icons**: Minimalistic SVG icons for files and folders

### 💻 Integrated Terminal
- **Web-based Terminal**: Full terminal experience using xterm.js
- **Multiple Terminal Tabs**: Create and manage multiple terminal sessions
- **Terminal Commands**: Built-in commands like ls, pwd, echo, clear, and more
- **Customizable**: Configurable font size, font family, and cursor settings
- **Keyboard Shortcut**: Toggle terminal with `Ctrl+\``

### 🔍 Advanced Search & Replace System
- **Find in Files**: Search across your entire workspace with regex support
- **Advanced Filters**: Include/exclude patterns for targeted searching
- **Search Options**: Case sensitivity, whole word matching, and regex support
- **Replace Functionality**: Replace text across multiple files
- **Results Panel**: Organized search results with file navigation
- **Keyboard Shortcut**: Open search with `Ctrl+Shift+F`

### 🎨 Theme System & Settings
- **Multiple Themes**: Dark, Light, and High Contrast themes
- **Comprehensive Settings**: Customize editor behavior, appearance, and functionality
- **Persistent Storage**: Settings saved to browser storage
- **Real-time Updates**: Changes apply immediately
- **Theme Variables**: CSS custom properties for consistent theming
- **Keyboard Shortcut**: Open settings with `Ctrl+,`

### 🧠 Code Intelligence & Tools
- **Enhanced Autocomplete**: Language-specific suggestions and snippets
- **Error Detection**: Real-time syntax checking and error highlighting
- **Code Formatting**: Format documents with `Ctrl+Shift+F`
- **Multiple Languages**: Support for JavaScript, TypeScript, Rust, Python, HTML, CSS, JSON, Markdown
- **Code Actions**: Quick fixes and refactoring suggestions
- **Minimap**: Enhanced minimap with better visualization
- **Bracket Matching**: Advanced bracket pair colorization

## Supported Languages

- Rust
- JavaScript/TypeScript
- Python
- HTML/CSS
- JSON/XML
- Markdown
- YAML/TOML
- Shell scripts
- C/C++
- Java
- Go
- PHP
- Ruby
- Swift
- Kotlin
- Scala
- SQL

## Installation

### Prerequisites

- [Node.js](https://nodejs.org/) (v16 or later)
- [Rust](https://rustup.rs/) (latest stable)
- [Tauri CLI](https://tauri.app/v1/guides/getting-started/prerequisites)

### Building from Source

1. Clone the repository:
   ```bash
   git clone https://github.com/rustcode/rustcode.git
   cd rustcode
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Run in development mode:
   ```bash
   npm run tauri dev
   ```

4. Build for production:
   ```bash
   npm run tauri build
   ```

## Usage

### Opening a Folder

- Use `Ctrl+Shift+O` or click "Open Folder" to select a workspace
- The file explorer will show the folder structure
- Click on files to open them in the editor

### File Operations

- **New File**: `Ctrl+N` or click the new file button
- **Save File**: `Ctrl+S`
- **Close Tab**: `Ctrl+W`
- **Find**: `Ctrl+F`
- **Find in Files**: `Ctrl+Shift+F`

### Command Palette

- Press `Ctrl+Shift+P` to open the command palette
- Type to search for commands
- Use arrow keys to navigate and Enter to execute

### Quick Open

- Press `Ctrl+P` to quickly open files by name
- Start typing the filename to filter results

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+N` | New File |
| `Ctrl+O` | Open File |
| `Ctrl+S` | Save File |
| `Ctrl+W` | Close Tab |
| `Ctrl+Shift+O` | Open Folder |
| `Ctrl+P` | Quick Open |
| `Ctrl+Shift+P` | Command Palette |
| `Ctrl+F` | Find |
| `Ctrl+Shift+F` | Find in Files |
| `Ctrl+H` | Replace |
| `Ctrl+G` | Go to Line |
| `Ctrl+B` | Toggle Sidebar |
| `Ctrl+J` | Toggle Panel |
| `Ctrl+=` | Zoom In |
| `Ctrl+-` | Zoom Out |

## Architecture

RustCode is built using:

- **Backend**: Rust with Tauri for native performance and system integration
- **Frontend**: HTML/CSS/JavaScript with Monaco Editor for text editing
- **File System**: Rust-based file operations with async support
- **UI Framework**: Custom components with modern CSS

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Code Style

- Rust code should follow `rustfmt` formatting
- JavaScript should use ES6+ features
- CSS should follow BEM methodology where applicable

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Inspired by [Visual Studio Code](https://code.visualstudio.com/)
- Built with [Tauri](https://tauri.app/)
- Text editing powered by [Monaco Editor](https://microsoft.github.io/monaco-editor/)
- Icons from various emoji sets

## Roadmap

- [ ] Plugin system
- [ ] Git integration
- [ ] Integrated terminal
- [ ] Debugging support
- [ ] Language server protocol support
- [ ] Themes and customization
- [ ] Split view editing
- [ ] Minimap enhancements
- [ ] Performance optimizations
- [ ] Cross-platform testing

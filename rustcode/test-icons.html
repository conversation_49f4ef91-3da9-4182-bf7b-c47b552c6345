<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RustCode Icon Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #cccccc;
            margin: 0;
            padding: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .icon-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: #2d2d30;
            border-radius: 4px;
            border: 1px solid #3e3e42;
        }
        .icon-item:hover {
            background: #37373d;
        }
        .icon-display {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        .icon-name {
            font-size: 13px;
            font-family: 'Consolas', 'Monaco', monospace;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #252526;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ffffff;
        }
        .validation-result {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .validation-success {
            background: #1e3a1e;
            border: 1px solid #4caf50;
            color: #4caf50;
        }
        .validation-error {
            background: #3a1e1e;
            border: 1px solid #f44336;
            color: #f44336;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #005a9e;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>RustCode Icon System Test</h1>
        
        <div class="test-section">
            <div class="test-title">Icon Validation</div>
            <div id="validation-results">Loading...</div>
            <button onclick="runValidation()">Re-run Validation</button>
            <button onclick="showAllIcons()">Show All Icons</button>
            <button onclick="testRandomIcon()">Test Random Icon</button>
        </div>

        <div class="test-section">
            <div class="test-title">Common Icons Test</div>
            <div id="common-icons" class="icon-grid"></div>
        </div>

        <div class="test-section">
            <div class="test-title">File Type Icons Test</div>
            <div id="file-type-icons" class="icon-grid"></div>
        </div>
    </div>

    <script type="module">
        import { createIcon, validateIcons, getAvailableIcons, getFileTypeIcon } from './src/icons/icon-library.js';

        // Make functions globally available
        window.createIcon = createIcon;
        window.validateIcons = validateIcons;
        window.getAvailableIcons = getAvailableIcons;
        window.getFileTypeIcon = getFileTypeIcon;

        function runValidation() {
            const results = validateIcons();
            const container = document.getElementById('validation-results');
            
            if (results.missing.length === 0) {
                container.innerHTML = `
                    <div class="validation-success">
                        ✅ All ${results.total} icons are valid and ready to use!
                    </div>
                `;
            } else {
                container.innerHTML = `
                    <div class="validation-error">
                        ❌ Found ${results.missing.length} missing/invalid icons: ${results.missing.join(', ')}
                    </div>
                    <div class="validation-success">
                        ✅ ${results.valid.length} icons are working correctly
                    </div>
                `;
            }
        }

        function showAllIcons() {
            if (window.RustCodeIcons) {
                window.RustCodeIcons.showAll();
            } else {
                alert('Icon debug functions not available');
            }
        }

        function testRandomIcon() {
            const icons = getAvailableIcons();
            const randomIcon = icons[Math.floor(Math.random() * icons.length)];
            if (window.RustCodeIcons) {
                window.RustCodeIcons.test(randomIcon, 32);
            } else {
                alert(`Testing icon: ${randomIcon}`);
            }
        }

        function displayCommonIcons() {
            const commonIcons = [
                'file', 'folder', 'folderOpen', 'chevronRight', 'chevronDown',
                'plus', 'close', 'refresh', 'search', 'settings', 'terminal',
                'save', 'open', 'edit', 'trash', 'copy', 'paste', 'cut'
            ];

            const container = document.getElementById('common-icons');
            container.innerHTML = '';

            commonIcons.forEach(iconName => {
                const item = document.createElement('div');
                item.className = 'icon-item';
                
                const iconDisplay = document.createElement('div');
                iconDisplay.className = 'icon-display';
                iconDisplay.innerHTML = createIcon(iconName, 24);
                
                const iconNameSpan = document.createElement('span');
                iconNameSpan.className = 'icon-name';
                iconNameSpan.textContent = iconName;
                
                item.appendChild(iconDisplay);
                item.appendChild(iconNameSpan);
                container.appendChild(item);
            });
        }

        function displayFileTypeIcons() {
            const fileTypes = [
                'test.js', 'test.ts', 'test.rs', 'test.py', 'test.html',
                'test.css', 'test.json', 'test.md', 'test.xml', 'test.yaml',
                'test.toml', 'test.sh', 'test.txt'
            ];

            const container = document.getElementById('file-type-icons');
            container.innerHTML = '';

            fileTypes.forEach(fileName => {
                const iconType = getFileTypeIcon(fileName);
                const item = document.createElement('div');
                item.className = 'icon-item';
                
                const iconDisplay = document.createElement('div');
                iconDisplay.className = 'icon-display';
                iconDisplay.innerHTML = createIcon(iconType, 24);
                
                const iconNameSpan = document.createElement('span');
                iconNameSpan.className = 'icon-name';
                iconNameSpan.textContent = `${fileName} (${iconType})`;
                
                item.appendChild(iconDisplay);
                item.appendChild(iconNameSpan);
                container.appendChild(item);
            });
        }

        // Make functions globally available
        window.runValidation = runValidation;
        window.showAllIcons = showAllIcons;
        window.testRandomIcon = testRandomIcon;

        // Initialize tests
        runValidation();
        displayCommonIcons();
        displayFileTypeIcons();
    </script>
</body>
</html>

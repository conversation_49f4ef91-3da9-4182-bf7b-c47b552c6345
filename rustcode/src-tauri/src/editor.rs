use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EditorTab {
    pub id: String,
    pub file_path: String,
    pub file_name: String,
    pub content: String,
    pub is_modified: bool,
    pub cursor_position: CursorPosition,
    pub scroll_position: ScrollPosition,
    pub language: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CursorPosition {
    pub line: usize,
    pub column: usize,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ScrollPosition {
    pub top: f64,
    pub left: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EditorState {
    pub tabs: HashMap<String, EditorTab>,
    pub active_tab_id: Option<String>,
    pub tab_order: Vec<String>,
}

impl Default for EditorState {
    fn default() -> Self {
        Self {
            tabs: HashMap::new(),
            active_tab_id: None,
            tab_order: Vec::new(),
        }
    }
}

impl EditorState {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn add_tab(&mut self, file_path: String, content: String) -> String {
        let tab_id = Uuid::new_v4().to_string();
        let file_name = std::path::Path::new(&file_path)
            .file_name()
            .unwrap_or_default()
            .to_string_lossy()
            .to_string();

        let language = detect_language_from_path(&file_path);

        let tab = EditorTab {
            id: tab_id.clone(),
            file_path,
            file_name,
            content,
            is_modified: false,
            cursor_position: CursorPosition { line: 0, column: 0 },
            scroll_position: ScrollPosition { top: 0.0, left: 0.0 },
            language,
        };

        self.tabs.insert(tab_id.clone(), tab);
        self.tab_order.push(tab_id.clone());
        self.active_tab_id = Some(tab_id.clone());

        tab_id
    }

    pub fn close_tab(&mut self, tab_id: &str) -> bool {
        if self.tabs.remove(tab_id).is_some() {
            self.tab_order.retain(|id| id != tab_id);
            
            // If this was the active tab, set a new active tab
            if self.active_tab_id.as_ref() == Some(&tab_id.to_string()) {
                self.active_tab_id = self.tab_order.last().cloned();
            }
            
            true
        } else {
            false
        }
    }

    pub fn set_active_tab(&mut self, tab_id: &str) -> bool {
        if self.tabs.contains_key(tab_id) {
            self.active_tab_id = Some(tab_id.to_string());
            true
        } else {
            false
        }
    }

    pub fn update_tab_content(&mut self, tab_id: &str, content: String) -> bool {
        if let Some(tab) = self.tabs.get_mut(tab_id) {
            tab.content = content;
            tab.is_modified = true;
            true
        } else {
            false
        }
    }

    pub fn mark_tab_saved(&mut self, tab_id: &str) -> bool {
        if let Some(tab) = self.tabs.get_mut(tab_id) {
            tab.is_modified = false;
            true
        } else {
            false
        }
    }

    pub fn get_tab(&self, tab_id: &str) -> Option<&EditorTab> {
        self.tabs.get(tab_id)
    }

    pub fn get_active_tab(&self) -> Option<&EditorTab> {
        self.active_tab_id.as_ref()
            .and_then(|id| self.tabs.get(id))
    }

    pub fn get_all_tabs(&self) -> Vec<&EditorTab> {
        self.tab_order.iter()
            .filter_map(|id| self.tabs.get(id))
            .collect()
    }

    pub fn has_unsaved_changes(&self) -> bool {
        self.tabs.values().any(|tab| tab.is_modified)
    }

    pub fn get_unsaved_tabs(&self) -> Vec<&EditorTab> {
        self.tabs.values()
            .filter(|tab| tab.is_modified)
            .collect()
    }
}

fn detect_language_from_path(file_path: &str) -> Option<String> {
    let path = std::path::Path::new(file_path);
    let extension = path.extension()?.to_str()?.to_lowercase();
    
    match extension.as_str() {
        "rs" => Some("rust".to_string()),
        "js" | "mjs" => Some("javascript".to_string()),
        "ts" => Some("typescript".to_string()),
        "py" => Some("python".to_string()),
        "html" | "htm" => Some("html".to_string()),
        "css" => Some("css".to_string()),
        "json" => Some("json".to_string()),
        "xml" => Some("xml".to_string()),
        "md" => Some("markdown".to_string()),
        "yaml" | "yml" => Some("yaml".to_string()),
        "toml" => Some("toml".to_string()),
        "sh" | "bash" => Some("shell".to_string()),
        "c" => Some("c".to_string()),
        "cpp" | "cc" | "cxx" => Some("cpp".to_string()),
        "h" | "hpp" => Some("c".to_string()),
        "java" => Some("java".to_string()),
        "go" => Some("go".to_string()),
        "php" => Some("php".to_string()),
        "rb" => Some("ruby".to_string()),
        "swift" => Some("swift".to_string()),
        "kt" => Some("kotlin".to_string()),
        "scala" => Some("scala".to_string()),
        "sql" => Some("sql".to_string()),
        "dockerfile" => Some("dockerfile".to_string()),
        _ => None,
    }
}

use crate::file_system::{self, <PERSON><PERSON><PERSON>, FileInfo, SearchResult};
use crate::editor::EditorState;
use tauri::State;
use std::sync::Mutex;

// Global editor state
type EditorStateType = Mutex<EditorState>;

#[tauri::command]
pub async fn get_directory_tree(path: String, max_depth: Option<usize>) -> Result<FileNode, String> {
    file_system::get_directory_tree(&path, max_depth)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn read_file_content(path: String) -> Result<String, String> {
    file_system::read_file_content(&path)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn write_file_content(path: String, content: String) -> Result<(), String> {
    file_system::write_file_content(&path, &content)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn create_file(path: String) -> Result<(), String> {
    file_system::create_file(&path)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn create_directory(path: String) -> Result<(), String> {
    file_system::create_directory(&path)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn delete_file(path: String) -> Result<(), String> {
    file_system::delete_file(&path)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn rename_file(old_path: String, new_path: String) -> Result<(), String> {
    file_system::rename_file(&old_path, &new_path)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_file_info(path: String) -> Result<FileInfo, String> {
    file_system::get_file_info(&path)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn search_in_files(
    directory: String,
    query: String,
    file_extensions: Option<Vec<String>>
) -> Result<Vec<SearchResult>, String> {
    file_system::search_in_files(&directory, &query, file_extensions)
        .map_err(|e| e.to_string())
}

// Editor state management commands
#[tauri::command]
pub async fn open_file_in_editor(
    path: String,
    state: State<'_, EditorStateType>
) -> Result<String, String> {
    let content = file_system::read_file_content(&path)
        .map_err(|e| e.to_string())?;

    let mut editor_state = state.lock().unwrap();
    let tab_id = editor_state.add_tab(path, content);

    Ok(tab_id)
}

#[tauri::command]
pub async fn close_editor_tab(
    tab_id: String,
    state: State<'_, EditorStateType>
) -> Result<bool, String> {
    let mut editor_state = state.lock().unwrap();
    Ok(editor_state.close_tab(&tab_id))
}

#[tauri::command]
pub async fn set_active_editor_tab(
    tab_id: String,
    state: State<'_, EditorStateType>
) -> Result<bool, String> {
    let mut editor_state = state.lock().unwrap();
    Ok(editor_state.set_active_tab(&tab_id))
}

#[tauri::command]
pub async fn update_editor_content(
    tab_id: String,
    content: String,
    state: State<'_, EditorStateType>
) -> Result<bool, String> {
    let mut editor_state = state.lock().unwrap();
    Ok(editor_state.update_tab_content(&tab_id, content))
}

#[tauri::command]
pub async fn save_editor_tab(
    tab_id: String,
    state: State<'_, EditorStateType>
) -> Result<(), String> {
    let mut editor_state = state.lock().unwrap();

    if let Some(tab) = editor_state.get_tab(&tab_id) {
        let path = tab.file_path.clone();
        let content = tab.content.clone();

        // Release the lock before doing file I/O
        drop(editor_state);

        file_system::write_file_content(&path, &content)
            .map_err(|e| e.to_string())?;

        // Re-acquire lock to mark as saved
        let mut editor_state = state.lock().unwrap();
        editor_state.mark_tab_saved(&tab_id);

        Ok(())
    } else {
        Err("Tab not found".to_string())
    }
}

#[tauri::command]
pub async fn get_editor_state(
    state: State<'_, EditorStateType>
) -> Result<EditorState, String> {
    let editor_state = state.lock().unwrap();
    Ok(editor_state.clone())
}

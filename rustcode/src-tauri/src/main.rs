// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod commands;
mod file_system;
mod editor;

use commands::*;
use editor::EditorState;
use std::sync::Mutex;

fn main() {
    let editor_state = Mutex::new(EditorState::new());

    tauri::Builder::default()
        .manage(editor_state)
        .invoke_handler(tauri::generate_handler![
            get_directory_tree,
            read_file_content,
            write_file_content,
            create_file,
            create_directory,
            delete_file,
            rename_file,
            search_in_files,
            get_file_info,
            open_file_in_editor,
            close_editor_tab,
            set_active_editor_tab,
            update_editor_content,
            save_editor_tab,
            get_editor_state
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

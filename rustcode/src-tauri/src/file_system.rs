use serde::{Deserialize, Serialize};
use std::fs;
use std::path::{Path, PathBuf};
use walkdir::WalkDir;
use anyhow::{Result, Context};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FileNode {
    pub name: String,
    pub path: String,
    pub is_directory: bool,
    pub children: Option<Vec<FileNode>>,
    pub size: Option<u64>,
    pub modified: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FileInfo {
    pub name: String,
    pub path: String,
    pub size: u64,
    pub modified: String,
    pub is_directory: bool,
    pub extension: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchResult {
    pub file_path: String,
    pub line_number: usize,
    pub line_content: String,
    pub match_start: usize,
    pub match_end: usize,
}

pub fn get_directory_tree(path: &str, max_depth: Option<usize>) -> Result<FileNode> {
    let path = Path::new(path);

    if !path.exists() {
        return Err(anyhow::anyhow!("Path does not exist: {}", path.display()));
    }

    let metadata = fs::metadata(path)?;
    let name = path.file_name()
        .unwrap_or_else(|| std::ffi::OsStr::new(""))
        .to_string_lossy()
        .to_string();

    if !metadata.is_dir() {
        return Ok(FileNode {
            name,
            path: path.to_string_lossy().to_string(),
            is_directory: false,
            children: None,
            size: Some(metadata.len()),
            modified: get_modified_time(&metadata),
        });
    }

    let mut children = Vec::new();

    // Check depth limit
    if let Some(depth) = max_depth {
        if depth == 0 {
            return Ok(FileNode {
                name,
                path: path.to_string_lossy().to_string(),
                is_directory: true,
                children: Some(children),
                size: None,
                modified: get_modified_time(&metadata),
            });
        }
    }

    if let Ok(entries) = fs::read_dir(path) {
        for entry in entries.flatten() {
            let entry_path = entry.path();

            // Skip hidden files and directories
            if let Some(name) = entry_path.file_name() {
                if name.to_string_lossy().starts_with('.') {
                    continue;
                }
            }

            if let Ok(child_node) = get_directory_tree(
                &entry_path.to_string_lossy(),
                max_depth.map(|d| d.saturating_sub(1))
            ) {
                children.push(child_node);
            }
        }
    }

    // Sort children: directories first, then files, both alphabetically
    children.sort_by(|a, b| {
        match (a.is_directory, b.is_directory) {
            (true, false) => std::cmp::Ordering::Less,
            (false, true) => std::cmp::Ordering::Greater,
            _ => a.name.to_lowercase().cmp(&b.name.to_lowercase()),
        }
    });

    Ok(FileNode {
        name,
        path: path.to_string_lossy().to_string(),
        is_directory: true,
        children: Some(children),
        size: None,
        modified: get_modified_time(&metadata),
    })
}

pub fn read_file_content(path: &str) -> Result<String> {
    fs::read_to_string(path)
        .with_context(|| format!("Failed to read file: {}", path))
}

pub fn write_file_content(path: &str, content: &str) -> Result<()> {
    // Create parent directories if they don't exist
    if let Some(parent) = Path::new(path).parent() {
        fs::create_dir_all(parent)?;
    }

    fs::write(path, content)
        .with_context(|| format!("Failed to write file: {}", path))
}

pub fn create_file(path: &str) -> Result<()> {
    if let Some(parent) = Path::new(path).parent() {
        fs::create_dir_all(parent)?;
    }

    fs::write(path, "")
        .with_context(|| format!("Failed to create file: {}", path))
}

pub fn create_directory(path: &str) -> Result<()> {
    fs::create_dir_all(path)
        .with_context(|| format!("Failed to create directory: {}", path))
}

pub fn delete_file(path: &str) -> Result<()> {
    let path = Path::new(path);

    if path.is_dir() {
        fs::remove_dir_all(path)
    } else {
        fs::remove_file(path)
    }
    .with_context(|| format!("Failed to delete: {}", path.display()))
}

pub fn rename_file(old_path: &str, new_path: &str) -> Result<()> {
    fs::rename(old_path, new_path)
        .with_context(|| format!("Failed to rename {} to {}", old_path, new_path))
}

pub fn get_file_info(path: &str) -> Result<FileInfo> {
    let path = Path::new(path);
    let metadata = fs::metadata(path)?;

    let name = path.file_name()
        .unwrap_or_else(|| std::ffi::OsStr::new(""))
        .to_string_lossy()
        .to_string();

    let extension = path.extension()
        .map(|ext| ext.to_string_lossy().to_string());

    Ok(FileInfo {
        name,
        path: path.to_string_lossy().to_string(),
        size: metadata.len(),
        modified: get_modified_time(&metadata).unwrap_or_default(),
        is_directory: metadata.is_dir(),
        extension,
    })
}

pub fn search_in_files(directory: &str, query: &str, file_extensions: Option<Vec<String>>) -> Result<Vec<SearchResult>> {
    let mut results = Vec::new();

    for entry in WalkDir::new(directory)
        .follow_links(false)
        .into_iter()
        .filter_map(|e| e.ok())
    {
        let path = entry.path();

        // Skip directories
        if path.is_dir() {
            continue;
        }

        // Filter by file extensions if provided
        if let Some(ref extensions) = file_extensions {
            if let Some(ext) = path.extension() {
                let ext_str = ext.to_string_lossy().to_lowercase();
                if !extensions.iter().any(|e| e.to_lowercase() == ext_str) {
                    continue;
                }
            } else if !extensions.is_empty() {
                continue;
            }
        }

        // Read file content and search
        if let Ok(content) = fs::read_to_string(path) {
            for (line_number, line) in content.lines().enumerate() {
                if let Some(match_start) = line.to_lowercase().find(&query.to_lowercase()) {
                    results.push(SearchResult {
                        file_path: path.to_string_lossy().to_string(),
                        line_number: line_number + 1,
                        line_content: line.to_string(),
                        match_start,
                        match_end: match_start + query.len(),
                    });
                }
            }
        }
    }

    Ok(results)
}

fn get_modified_time(metadata: &fs::Metadata) -> Option<String> {
    metadata.modified()
        .ok()
        .and_then(|time| {
            let datetime: chrono::DateTime<chrono::Utc> = time.into();
            Some(datetime.format("%Y-%m-%d %H:%M:%S").to_string())
        })
}

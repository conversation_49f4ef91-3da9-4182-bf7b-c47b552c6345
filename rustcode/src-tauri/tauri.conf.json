{"build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devPath": "http://localhost:1420", "distDir": "../dist", "withGlobalTauri": false}, "package": {"productName": "RustCode", "version": "0.1.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "dialog": {"all": false, "open": true, "save": true}, "fs": {"all": true, "scope": ["**"]}, "path": {"all": true}, "window": {"all": false, "close": true, "hide": true, "show": true, "maximize": true, "minimize": true, "unmaximize": true, "unminimize": true, "startDragging": true}}, "bundle": {"active": true, "targets": "all", "identifier": "com.rustcode.dev", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "title": "RustCode", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600}]}}
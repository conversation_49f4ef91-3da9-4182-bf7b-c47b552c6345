[package]
name = "rustcode"
version = "0.1.0"
description = "A minimalistic code editor written in Rust"
authors = ["RustCode Team"]
license = "MIT"
repository = "https://github.com/rustcode/rustcode"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.0", features = [] }

[dependencies]
tauri = { version = "1.0", features = [ "fs-all", "window-minimize", "window-start-dragging", "window-maximize", "window-hide", "shell-open", "dialog-save", "window-show", "dialog-open", "window-unminimize", "window-unmaximize", "window-close", "path-all"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
walkdir = "2.3"
notify = "5.0"
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
thiserror = "1.0"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
